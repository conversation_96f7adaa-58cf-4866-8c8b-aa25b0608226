---
#Syntax for assigning multiple permissions
# mysql_user_privileges:
#   - '*.*:SELECT,INSERT,UPDATE,DELETE,CREATE,DROP,RELOAD,SHUTDOWN,PROCESS,FILE,REFERENCES,INDEX,ALTER,SHOW DATABASES,<PERSON>UP<PERSON>,CREATE TEMPORARY TABLES,LOCK TABLES,EXECUT<PERSON>,REPLICATION SLAVE,REPLICATION CLIENT,CREATE VIEW,SHOW VIEW,CREATE ROUTINE,ALTER ROUTINE,CREATE USER,EVENT,TRIGGER,CREATE TABLESPACE'
mysql_admin_privileges:
  - '{{ magento_db_name }}.*:ALL'
  - '*.*:ALL'

env_params:
  test01:
    expire_logs_days: 3
  stage01:
    expire_logs_days: 3
  prod:
    expire_logs_days: 10
