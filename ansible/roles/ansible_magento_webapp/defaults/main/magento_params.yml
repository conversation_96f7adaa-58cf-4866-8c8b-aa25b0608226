---
# Package versions
# php_version: 7.4
# php_version_hold: 8*
# nginx_version: 1.18.0-0ubuntu1.2 # find available nginx versions with `apt-cache madison nginx`

#PHP parameters
# php_memory_limit: 4G
# php_realpath_cache_size: 10M
# php_realpath_cache_ttl: 7200
# php_opcache_save_comments: 1
# php_upload_max_filesize: 10M
# php_post_max_size: 10M

magento_env:
    shared:
      magento_db_name: magento
      magento_db_user: magento
      magento_admin_firstname: Magento
      magento_admin_lastname: Admin
      magento_admin_email: "magento-admin-{{domain}}@ipiccolo.com"
      magento_admin_user: magento-admin
      magento_timezone: Europe/Stockholm 
      magento_language: en_US
      magento_currency: SEK
      magento_use_rewrites: 1
      magento_search_engine: elasticsearch7
      magento_elasticsearch_port: 9200
      magento_redis_port: 6379
      magento_app_service_usernames:
        - magento-www
        - magento-cli
        - gitlab-runner
        - root
      local_magento_webapp_group: magento
      local_magento_webapp_gid: 2303
      local_magento_webapp_console_user: magento-cli
      local_magento_webapp_console_uid: 2302
      local_magento_webapp_console_gid: 2302
      local_magento_webapp_nginx_user: www-data
      local_magento_webapp_nginx_uid: 2301
      local_magento_webapp_nginx_gid: 2301
      local_magento_webapp_nginx_home: /var/www
      local_magento_webapp_nginx_group: www-data
      nginx_domain_list:
        - buysnus_ch
        - buysnus_com
        - mysnus_com
        - nicobags_com
        - snus_at
        - snus_ch
        - snus_de
        - snuscentral_com
        - snusexpress_ch
        - snusexpress_com
        - snusexpress_no
        - snusexpress_se
        - snushof_ch
