diff --git a/vendor/hyva-themes/magento2-hyva-checkout/src/Magewire/Component/AbstractForm.php b/vendor/hyva-themes/magento2-hyva-checkout/src/Magewire/Component/AbstractForm.php
index 4f1c56dc5..b308447a7 100755
--- a/vendor/hyva-themes/magento2-hyva-checkout/src/Magewire/Component/AbstractForm.php
+++ b/vendor/hyva-themes/magento2-hyva-checkout/src/Magewire/Component/AbstractForm.php
@@ -193,6 +193,10 @@ abstract class AbstractForm extends Component\Form implements EvaluationInterfac
         $form->setAttribute('x-data', 'initMagewireForm($el, $wire)');
         $form->setAttribute('x-init', 'initialize()');
 
+        $this->getEvaluationResultBatch()->push(
+            $this->getEvaluationResultBatch()->getFactory()->createValidation('magewire-form-validation')
+        );
+
         // Only dispatch a Magewire form modification hook, ensuring backward compatibility
         // since the form built by itself already dispatches a form:build event.
         $this->dispatchFormModificationHook('build');
