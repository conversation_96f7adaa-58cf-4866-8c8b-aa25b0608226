@layer utilities {
    /* This class only exists for backwards compatibility, */
    /* The default is applied in tailwind.config.js since Hyvä 1.2.0 */
    .text-sans-serif {
        font-family: "Poppins", "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    }
}

html {
    @apply antialiased;
}

body {
    @apply text-black leading-normal text-base tracking-normal;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Poppins-Light.ttf");
    font-weight: 300;
    font-display: swap;
  }
  
  @font-face {
    font-family: "Poppins";
    src: url("../fonts/Poppins-Regular.ttf");
    font-weight: 400;
    font-display: swap;
  }
  
  @font-face {
    font-family: "Poppins";
    src: url("../fonts/Poppins-Medium.ttf");
    font-weight: 500;
    font-display: swap;
  }
  
  @font-face {
    font-family: "Poppins";
    src: url("../fonts/Poppins-SemiBold.ttf");
    font-weight: 600;
    font-display: swap;
  }
  
  @font-face {
    font-family: "Poppins";
    src: url("../fonts/Poppins-Bold.ttf");
    font-weight: 700;
    font-display: swap;
  }
  
  /* @font-face {
    font-family: "Poppins";
    src: url("../fonts/Poppins-ExtraBold.ttf");
    font-weight: 800;
    font-display: swap;
  }
   */
  /* @font-face {
    font-family: "Poppins";
    src: url("../fonts/Poppins-Black.ttf");
    font-weight: 900;
    font-display: swap;
  } */