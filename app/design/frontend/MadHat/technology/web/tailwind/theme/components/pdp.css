#quotecart-drawer {
  [role="dialog"] {
    @apply z-50;
  }
}

.amshopby-brand-title-link {
  @apply text-cblue;
}

.pdp-product-title {
  > .container {
    @apply mt-0 text-left pl-0;
  }

  .text-gray-900 {
    @apply text-cgrey-65;
  }
  h1 {
    @apply text-3xl;
  }
}

.pdp-article-ean-out {
  @apply flex flex-wrap lg:flex-nowrap gap-0 lg:gap-6 w-full text-xs sm:text-sm pb-4 border-b border-cgrey-75 justify-between;

  >div {
    @apply w-3/5 lg:w-auto;

    &:first-child {
      @apply w-2/5 lg:w-auto;
    }
    &:last-child{
      @apply ml-[40%] lg:ml-0;
    }
  }
}

.price-section {
  .price-label {
    @apply hidden;
  }

  .price-box {
    &.price-final_price {
      @apply flex flex-row-reverse justify-end items-center gap-4;
    }
  }
}

.price-container {
  @apply flex flex-row-reverse justify-end items-center gap-4;
}

.pdp-price {
  @apply text-[28px] leading-8 font-semibold text-cred;
}

.vat-message {
  @apply text-xs w-full text-cgrey-65 mb-1;
}

.catalog-product-view {
  .page-main {
    @apply mt-0;
  }
}

.add-to-cart {
  @apply justify-center items-center border border-primary lg:min-w-56;
}

.addtocart-stepper {
  @apply mr-2 sm:mr-4;

  .form-input {
    @apply rounded-none;
  }
}

.supply-deals {
  .related-product-slider,
  .upsell-product-slider {
    .container {
      &.flex {
        &.flex-col {
          @apply hidden;
        }
      }
    }

    .product-sub-title,
    .star-rating,
    .product-stock,
    .product-item-wishlist-compare,
    .product-variants,
    .more-options,
    .comparison-price-out,
    .variants-more-option-container,
    .product-add-to-cart-btn {
      @apply hidden;
    }

    .product-brand-name {
      @apply text-[10px] leading-none min-h-[10px] mr-0;
    }
    .product-title {
      @apply line-clamp-2 mr-0 text-xs font-semibold min-h-8;
      .product-item-link {
        @apply text-[length:inherit] leading-none;
      }
    }

    .product-price {
      @apply text-cred text-xs font-semibold pr-2;
    }
    .product-price-box-wrapper {
      .price-box {
        &.price-final_price {
          .price-final_price {
            @apply text-base;
          }
        }
      }
    }


    .product-price-out {
      @apply flex-wrap w-full items-center justify-center;
    }

    .product-min-height {
      @apply min-h-0;
    }

    .previous-btn,
    .next-btn {
      @apply absolute top-1/3 bg-white py-4 rounded;
    } 
    .previous-btn{
      @apply -left-5 xl:-left-6;
    }

    .next-btn{
      @apply -right-5 xl:-right-6;
    }

    .product-item-top {
      @apply p-0;
    }
  }
}

.description-wrapper,
#product-attributes {
  @apply text-cgrey-65;
  .prose,
  .max-w-prose {
    @apply max-w-full text-cgrey-65;
  }

  iframe {
    @apply w-full;
  }
}

.product-description {
  ul {
    @apply list-[inherit] list-inside;
    li {
      @apply my-0;
    }
  }
}

.stock-status-out {
  .text-right {
    .items-center.justify-end {
      @apply justify-start;
    }
  }
}

.info-icon {
  &:hover {
    .tooltip-container {
      @apply transition-all ease-in-out duration-700 flex;
    }
  }
}
.tooltip-container {
  @apply hidden absolute w-auto rounded bg-cgrey-65 p-1 text-xs text-white min-w-36 z-10 left-1/2 -translate-x-2/4;

  &:before {
    @apply z-0 absolute w-2 h-2 -top-1 left-0 right-0 m-auto bg-cgrey-65 rotate-45 content-[''];
  }
}

.pdp-swatch-icon {
  &.madhat_color {
    svg {
      @apply fill-cgrey-65;
    }
  }
  svg {
    @apply stroke-cgrey-65;
  }

  + span {
    &:after {
      @apply relative content-[":"];
    }
  }
}

.pdp-swatch-row {
  @apply flex flex-col lg:flex-row items-center py-4 lg:py-1 w-full border-cgrey-75;

  .pdp-swatch-left-col {
    @apply w-full lg:w-1/3 text-left text-gray-700 label mb-0 flex gap-2 items-center;

      &:not(:has(.madhat_color)) {

      .label-truncate {
        @apply lg:max-w-24 lg:truncate; 
      }
    }
  }
  
  .pdp-swatch-right-col {
    @apply w-full lg:ml-2 lg:w-2/3 text-left text-gray-900;
  }
}

.madhat_color {
  .pdp-swatch-row {
    @apply flex-wrap;

    .pdp-swatch-left-col,
    .pdp-swatch-right-col {
      @apply sm:w-full sm:ml-0;
    }

    .pdp-swatch-left-col {
      @apply mt-0 md:mt-3;
    }

    .pdp-swatch-right-col {
      @apply my-3 mx-0 lg:ml-1;
    }
  }
}

.pdp-swatch-row {
  .pdp-swatch-right-col {
    @apply m-2;
  }
}

.show-more-variants {
  @apply flex justify-center items-center;

  a {
    @apply text-cblue text-lg ;
    span {
      @apply underline underline-offset-1;
    }
    i {
      @apply leading-8 pl-1;
    }
  }
}

.price-vat-stock-container {
  @apply mb-4 lg:mb-0 lg:mr-8 w-full lg:w-auto;

  .final-price {
    .price-wrapper {
      .price {
        @apply text-[28px] leading-8 font-semibold;
      }
    }
  }
}