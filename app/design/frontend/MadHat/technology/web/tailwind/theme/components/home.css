.cms-home{
  .page-title {
    @apply hidden;
  }
  .page-main {
    @apply mt-0;
  }
  .page-main {
    @apply px-0 mt-[-13px];
  }
  .column.main{
    > [data-content-type='row'][data-appearance='contained']{
      @apply px-0 py-0;
      > div{
        @apply px-0 pb-0 mb-0;        
      }
    }

    [data-content-type='row'] {
      > div {
        @apply px-0;
      }
    }
  }

  .brand-slider-container {
    @apply flex-auto grow-0 shrink-0 w-[calc(50%_-_theme(spacing.4))] sm:w-[calc(50%_-_theme(spacing.10))] lg:w-[calc(12%-7px)] flex justify-center items-center;
  }

  .home-brand-slide-nav {
    .previous-button,
    .next-button {
      @apply absolute top-10;
    }

    .previous-button {
      @apply left-0;
    }

    .next-button {
      @apply right-0;
    }
  }

  .blog-widget-recent {
    .js_slide {
      @apply relative mt-0;
      .post-header {
        @apply absolute leading-5 left-3 top-3 p-2 bg-cgrey-90 font-bold text-xl text-white text-center max-w-16 uppercase;
      }
      .post-content {
        @apply bg-cgrey-50 p-1 h-[450px] lg:h-[520px];

        .post-ftimg-hld {
          @apply mb-3 h-[240px] lg:h-[320px] bg-white w-full flex items-center justify-center;
          a {
            img {
              @apply w-full max-h-[240px] lg:max-h-[320px] object-contain bg-white;
            }
          }
        }
      }
      .post-title {
        @apply text-base font-bold mb-2 lg:mb-3 px-2 lg:px-3 h-[48px] line-clamp-2;
      }
  
      .post-text-container {
        @apply text-sm px-2 lg:px-3 max-h-[200px] line-clamp-[5] lg:line-clamp-4 h-[102px] lg:h-[80px];
  
        p, div {
          @apply m-0 text-sm font-normal;
        }
      }

      .post-read-more {
        @apply mb-3 ml-2 lg:ml-3 mt-2;
      }
  
      .post-holder {
        @apply mr-1 lg:mr-2 w-full;
      }
    }    
  }

  .post-read-more {
    @apply text-corange text-sm inline-flex mt-1 ml-1;
  }
}