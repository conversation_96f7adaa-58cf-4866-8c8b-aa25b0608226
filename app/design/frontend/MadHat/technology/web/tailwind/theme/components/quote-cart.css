/*--Custom Quote Cart.css--*/
.amasty_quote-cart-index{ 
  h1.page-title{
    @apply text-3xl text-center font-bold w-full border-b border-cgrey-65 mb-6;
  }
  .cart-price-value{
    .price-excluding-tax,
    .price-including-tax {    
        .price {
            @apply font-normal; 
        }
    }
  }
  .addtocart-stepper{
    .btn{
      .svg{
        @apply text-cgrey-0;
      }
    }
  }

  .quote-cart-col-1 {
    @apply flex items-center;
  }

  .quote-cart-col-2 {
    @apply grow;

    .price-section {
      @apply pt-0;
    }
  }

  .request-quote-price,
  .request-quote-qty {
    @apply relative;
    label {
      @apply absolute left-0 top-[-10px] mb-0 bg-white text-sm font-semibold px-2;
    }
  }

  .request-quote-price {
    label {
      @apply left-12;
    }
  }

  .request-quote-qty {
    label {
      @apply left-4;
    }
  }

  .add-note {
    @apply relative mt-4;
    .form-input:focus {
      @apply border-2 border-primary-darker;
      box-shadow: none;
    }
  }
  
  .add-note {
    .form-input+label.label {
      @apply bg-cgrey-0 absolute top-2.5 left-1 font-semibold text-xs tracking-wide text-cgrey-65 pointer-events-none transition duration-200 ease-in-out px-2;
    }
  }
  
  .add-note:focus-within label.label,
  .add-note input:not(:placeholder-shown)+label.label  {
    transform: translateY(-1.2rem) scale(0.9);
    @apply bg-cgrey-0 leading-4 left-1 text-cgrey-90 tracking-wide;
  }
}

.quote-price,
.quote-qty {
  @apply mr-2 text-lg inline-block text-center min-w-28 border border-cgrey-65 rounded py-1 px-2;
}

.quote-qty {
  @apply min-w-20;
}