<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\BssSimpledetailconfigurable\ViewModel\Helper as ModuleViewModel;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var Product $product */
$product = $this->hasData('product')
    ? $this->getData('product')
    : $currentProduct->get();

$moduleViewModel = $viewModels->require(ModuleViewModel::class);
$moduleConfig = $moduleViewModel->getModuleConfig();
?>
<?php $isSalable = $product->getIsSalable() ?>
<?php if ($product->getTypeId() == 'configurable' && $moduleConfig->isShowName() && $moduleConfig->isModuleEnable()): ?>
    <button type="submit"
            form="product_addtocart_form"
            title="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>"
            class="btn btn-primary btn-size-md add-to-cart"
            id="product-addtocart-button"
            x-data="{stock_status: <?= $isSalable ? '1' : '0' ?>}"
            :class="{'disable hidden': !stock_status}"
            x-bind:disabled=!stock_status
            @simple-detail-product-active.window="stock_status = $event.detail.event_product_data.stock_status"
    >
<?php else: ?>
    <button type="submit"
            form="product_addtocart_form"
            title="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>"
            class="btn btn-primary btn-size-md add-to-cart<?= ($isSalable ? '' : ' disable hidden'); ?>"
            <?= ($isSalable ? '' : 'disabled="disabled"'); ?>
            id="product-addtocart-button"
    >
<?php endif; ?>
    <span class=""><?= $block->getData('is_cart_configure') ?
            $escaper->escapeHtml(__('Update item')) :
            $escaper->escapeHtml(__('Add to Cart')) ?>
    </span>
</button>

<?= $block->getChildHtml('', true) ?>
