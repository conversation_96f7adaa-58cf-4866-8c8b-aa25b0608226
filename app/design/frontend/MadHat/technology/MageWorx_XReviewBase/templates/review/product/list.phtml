<?php
/**
 * Copyright © MageWorx. All rights reserved.
 * See https://www.mageworx.com/terms-and-conditions for license details.
 */

/** @var $block \MageWorx\XReviewBase\Block\Review\Product\ListView */
/** @var \Magento\Framework\Escaper $escaper */

//@formatter:off
$ratings       = $block->getRatings();
$overallRating = $block->getOverallRating();

/** @var \Magento\Review\Model\Review[] $items */
$items  = $block->getReviewsCollection()->getItems();
$format = $block->getDateFormat() ?: \IntlDateFormatter::SHORT;

/** @var \Hyva\Theme\ViewModel\HeroiconsSolid $heroicons */
$heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsSolid::class);
?>

<?php if (count($items)) : ?>
    <script>
        "use strict";

        function initMwReview() {
            return {
                init() {
                    const $mwReviewUpdate = document.getElementById("reviewUpdate");
                    if (mwReviewConfig.isNeedScrollToReview) $mwReviewUpdate.scrollIntoView();

                    // toggle pager to ajax mode
                    const $pagesUl = $mwReviewUpdate?.querySelector(".pages-items");
                    const $pageHrefs = $pagesUl?.querySelectorAll("a");
                    $pageHrefs?.forEach(($elem) => {
                        $elem.addEventListener("click", event => {
                            event.preventDefault();
                            this.filter.onFilterChange($elem.getAttribute("href"));
                        })
                    })

                    // parse all images from reviews
                    const imagesOnPage = [];
                    const $images = $mwReviewUpdate.querySelectorAll("a.mw-review-media-image");
                    $images?.forEach($elem => {
                        const $img = $elem.querySelector("img");
                        const orig = $elem.getAttribute("href");
                        const thumb = $img.getAttribute("src");
                        const label = $img.getAttribute("alt");
                        imagesOnPage.push({ orig, thumb, label });
                    })
                    this.gallery.images = imagesOnPage;

                    // initialize all filters
                    this.filter.parent = this;
                    const filters = this.filter.filters;
                    this.filter.ajaxUrl = mwReviewConfig.reviewUrl;
                    filters.sortBy = this.$refs.mwReviewSorter?.querySelector('option[selected]')?.value;
                    if (this.$refs?.mwReviewSorterDSC) filters.sortOrder = "desc";
                    else if (this.$refs?.mwReviewSorterASC) filters.sortOrder = "asc"
                    filters.isOnlyYourCountry = this.$refs.mwReviewIsCountry?.checked;
                    filters.isOnlyWithImages = this.$refs.mwReviewIsImg?.checked;
                    filters.isOnlyVerifiedBuyers = this.$refs.mwReviewIsVer?.checked;
                },

                filter: {
                    parent: null,
                    ajaxUrl: "",
                    filters: {
                        isOnlyYourCountry: false,
                        isOnlyWithImages: false,
                        isOnlyVerifiedBuyers: false,
                        sortBy: null,
                        sortOrder: null,
                    },
                    onFilterChange(newUrl) {
                        const url = new URL(this.ajaxUrl);
                        const params = new URLSearchParams(url.search);
                        const filters = this.filters;
                        const paramNames = {
                            page: mwReviewToolbarObj.page,
                            limit: mwReviewToolbarObj.limit,
                            direction: mwReviewToolbarObj.direction,
                            filter: mwReviewToolbarObj.filter,
                            locationFilter: mwReviewToolbarObj.locationFilter,
                            mediaFilter: mwReviewToolbarObj.mediaFilter,
                            order: mwReviewToolbarObj.order,
                        }

                        // add page to params after click on pager
                        if (newUrl) {
                            const url = new URL(newUrl);
                            const urlParams = new URLSearchParams(url.search);
                            // get param with page num
                            if (urlParams.get(paramNames.page)) {
                                params.append(paramNames.page, urlParams.get(paramNames.page));
                            }
                        }

                        if (mwReviewToolbarObj.url) {
                            const url = new URL(mwReviewToolbarObj.url);
                            const urlParams = new URLSearchParams(url.search);
                            // get param with limit
                            if (urlParams.get(paramNames.limit)) {
                                params.append(paramNames.limit, urlParams.get(paramNames.limit));
                            }
                        }

                        // use only filters params, without get
                        if (filters.isOnlyYourCountry) params.append(paramNames.locationFilter, "on");
                        if (filters.isOnlyWithImages) params.append(paramNames.mediaFilter, "on");
                        if (filters.isOnlyVerifiedBuyers) params.append(paramNames.filter, "on");
                        if (filters.sortBy) params.append(paramNames.order, filters.sortBy);
                        if (filters.sortOrder) params.append(paramNames.direction, filters.sortOrder);

                        this.fetchFilteredReviews(url + "?" + params.toString());
                    },
                    fetchFilteredReviews(url) {
                        this.parent.isLoading = true;
                        mwReviewConfig.isNeedScrollToReview = true;

                        fetch(url, {
                            method: 'GET',
                            headers: {
                                "X-Requested-With": "XMLHttpRequest",
                            }
                        })
                            .then(response => {
                                if (! response.ok) console.warn('request failed');
                                return response.text();
                            })
                            .then(data => {
                                const $dataDiv = document.getElementById("reviewUpdate");
                                // replace all review block
                                $dataDiv.innerHTML = data;
                            })
                            .catch(error => {
                                console.log(error)
                            })
                    },
                },

                galleryEventListeners: {
                    ['@keydown.window.escape']() {
                        this.gallery.currentImage.isShow = false;
                    },
                },
                gallery: {
                    images: [],
                    currentImage: {
                        isShow: false,
                        label: "",
                        orig: "",
                        thumb: "",
                        index: null,
                    },
                    openImage(link) {
                        // get original image href and set as current
                        this.currentImage.orig = link;

                        // set current index in images array
                        const index = this.images.findIndex(elem => elem.orig === link);
                        this.currentImage.index = index;

                        this.currentImage.isShow = true;
                    },
                    openNextImage() {
                        const index = this.currentImage.index;
                        // check is last
                        if (index === this.images.length - 1) {
                            this.openImage(this.images[0].orig);
                        }
                        else this.openImage(this.images[index+1].orig);
                    },
                    openPrevImage() {
                        const index = this.currentImage.index;
                        // check is first
                        if (index === 0) {
                            this.openImage(this.images[this.images.length - 1].orig);
                        }
                        else this.openImage(this.images[index - 1].orig);
                    }
                },

                isLoading: false,
            }
        }
    </script>

    <div id="customer-reviews" x-data="initMwReview()" x-init="init()" class="mw-reviews" x-cloak>
        <!--Loading indicator-->
        <?= $block->getChildHtml('loading') ?>

        <div class="container mx-auto flex pt-6 pb-3 mb-6 md:flex-row border-b-2 border-gray-300 mw-reviews-title">
            <h3 class="text-gray-900 text-2xl title-font font-base text-center md:text-left w-full">
                <?= $escaper->escapeHtml(__('Customer Reviews')) ?>
            </h3>
        </div>

        <div class="card w-full px-6 py-3 mw-reviews-card">
            <!-- Rating -->
            <div class="flex inline-flex items-center mw-reviews-rating">
                <?= $heroicons->starHtml('w-10 h-10 text-yellow-400', null, null) ?>
                <div class="mw-reviews-rating-summary">
                    <span class="text-3xl inline-flex items-end">
                        <?= (float)$overallRating['rating_summary'] ?>/5
                    </span>
                    <span class="ml-4 inline-flex items-end">
                        <?= __('Based on %count reviews', ['count' => (int)$overallRating['reviews_count']]); ?>
                    </span>
                </div>
            </div>
            <div>
                <ul class="flex flex-col gap-y-0 border-b pb-4 mw-reviews-rating-stars">
                    <?php foreach ($ratings as $key => $rating): ?>
                        <li class="flex flex-wrap gap-x-4">
                            <!-- stars -->
                            <div class="inline-flex items-center">
                                <?php for ($i = 5; $i > $key; $i--) {
                                    echo $heroicons->starHtml('w-6 h-6 text-yellow-400', null, null);
                                }?>
                                <?php for ($i = 1; $i <= $key; $i++) {
                                    echo $heroicons->starHtml('w-6 h-6 text-gray-400', null, null);
                                }?>
                            </div>
                            <!-- percents -->
                            <div>
                                <div class="inline-flex items-center w-40 mw-reviews-rating-stars-per">
                                    <div class="h-2 bg-yellow-400" style="width:<?= (int)$rating['percent'] ?>%;"></div>
                                    <div class="h-2 bg-gray-400" style="width:<?= 100 - (int)$rating['percent'] ?>%;"></div>
                                </div>
                                <div class="inline-flex items-center ml-2 mw-reviews-rating-stars-per-count">
                                    <?= (int)$rating['percent'] ?>% (<?= (int)$rating['stage_count'] ?>)
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <!-- Rating -->

            <?= $block->getChildHtml('toolbar') ?>

            <!-- Reviews -->
            <div class="mt-4 mw-reviews-list">
                <?php foreach ($items as $review): ?>
                    <div class="border-b pb-6 pt-4 my-4 border-container last:border-0 last:mb-0 mw-review"
                         itemscope itemprop="review" itemtype="http://schema.org/Review">
                        <!-- rating -->
                        <?php if (count($review->getRatingVotes())): ?>
                            <?php foreach ($review->getRatingVotes() as $vote): ?>
                                <div class="mw-review-rating" itemprop="reviewRating" itemscope itemtype="http://schema.org/Rating">
                                    <meta itemprop="worstRating" content="1"/>
                                    <meta itemprop="bestRating" content="100"/>
                                    <div class="hidden">
                                        Rating
                                    </div>
                                    <div class="flex flex-row" aria-hidden="true">
                                        <?php $revRatingPer = $vote->getPercent(); ?>
                                        <?php for ($i = 0; $i < $revRatingPer; $i+=20) {
                                            echo $heroicons->starHtml('w-6 h-6 text-yellow-400', null, null);
                                        }?>
                                        <?php for ($i = 100; $i > $revRatingPer; $i-=20) {
                                            echo $heroicons->starHtml('w-6 h-6 text-gray-400', null, null);
                                        }?>
                                    </div>
                                    <div class="hidden" itemprop="ratingValue">
                                        <?= $escaper->escapeHtml($vote->getPercent()) ?>%
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <!-- rating -->

                        <!-- START Variant attributes info -->
                        <div class="text-lg my-2 mw-variant-info">
                            <?php $variantInfo = $review->getVariantInfo(); ?>
                            <?php if(!empty($variantInfo)): ?>
                                <?php $variantInfoArray = json_decode($review->getVariantInfo(), true);  ?>
                                <p class="capitalize">
                                    <?php foreach ($variantInfoArray as $v): ?>
                                        <?php $separator = ($v != end($variantInfoArray)) ? " | " : ''; ?>
                                        <?php if (($pos = strpos($v, ":")) !== FALSE) : ?>
                                            <?php $info = explode(":",$v); ?>
                                            <?php  if($info[0] != 'sku'): ?>
                                                <span><?= $info[1] . $separator; ?></span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        <!-- END Variant attributes info -->

                        <!-- title -->
                        <div class="text-lg my-2 mw-review-title" itemprop="name">
                            <?= $escaper->escapeHtml($review->getTitle()) ?>
                        </div>
                        <!-- title -->


                        <!-- date & isRecommended -->
                        <div class="flex items-center flex-wrap gap-x-4 mw-review-date">
                            <span class="text-gray-700">
                                <time itemprop="datePublished"
                                      datetime="<?= $escaper->escapeHtml(date("d/m/Y", strtotime($review->getCreatedAt()))) ?>">
                                    <?php //= $escaper->escapeHtml($block->formatDate($review->getCreatedAt(), $format)) ?>
                                    <?= $escaper->escapeHtml(date("d/m/Y", strtotime($review->getCreatedAt()))) ?>
                                </time>
                            </span>

                            <?php if ($block->allowRecommendLabel($review)) : ?>
                                <div class="px-2 py-0.5 bg-green-500 rounded-md inline-flex items-center mw-review-recommended">
                                    <span class="text-white">
                                        <?= $escaper->escapeHtml(__('Recommend')) ?>
                                    </span>
                                    <?= $heroicons->thumbUpHtml('w-4 h-4 text-white ml-0.5', null, null); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <!-- date & isRecommended -->

                        <!-- author -->
                        <div class="mt-4 flex items-center flex-wrap gap-x-4 mw-review-author" itemprop="author" itemscope itemtype="https://schema.org/Person">
                            <span>Review by</span>
                            <span itemprop="name" class="font-bold">
                                <?= $escaper->escapeHtml($review->getNickname()) ?>
                            </span>

                            <?php if ($block->allowLocation($review) && $review->getLocationText()) : ?>
                                <div class="inline-flex items-start mw-review-location">
                                    <span class="ml-0.5 flex flex-wrap gap-x-0.5"><?= /* @noEscape */ $review->getLocationText() ?></span>
                                </div>
                            <?php endif; ?>

                            <?php if ($block->allowVerifiedLabel($review)) : ?>
                                <div class="inline-flex items-start mw-review-verified">
                                    <?= $heroicons->badgeCheckHtml('w-5 h-5 text-green-500 ml-0.5', null, null); ?>
                                    <span class="ml-0.5"><?= $escaper->escapeHtml(__('Verified Buyer')) ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <!-- author -->

                        <!-- review details -->
                        <div class="mt-4 mw-review-details" itemprop="description">
                            <?= /* @noEscape */ nl2br($escaper->escapeHtml($review->getDetail())) ?>
                        </div>
                        <!-- review details -->

                        <?php if ($block->allowMediaGallery($review)): ?>
                            <!-- gallery -->
                            <div class="mt-4 pt-2 flex flex-row flex-wrap gap-x-4 gap-y-2 mw-review-gallery">
                                <?php foreach ($review->getMediaGallery() as $mediaItem): ?>
                                    <a class="border hover:border-primary mw-review-media-image"
                                       @click.prevent="gallery.openImage('<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($mediaItem->getMediaUrl())) ?>')"
                                       href="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($mediaItem->getMediaUrl())) ?>">
                                        <img class="h-24 w-auto" src="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($mediaItem->getThumbnailMediaUrl())) ?>"
                                             alt="<?= $escaper->escapeHtmlAttr($mediaItem->getLabel()) ?>"/>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                            <!-- gallery -->
                        <?php endif; ?>

                        <?php if ($block->isEnableProsAndCons($review)): ?>
                            <!-- pros and cons -->
                            <div class="mw-review-pros-cons">
                                <div class="mt-4 flex flex-col items-start md:flex-row w-full gap-x-4 gap-y-4">
                                    <div class="inline-flex items-start md:w-1/2 mw-review-pros">
                                        <div>
                                            <?= $heroicons->plusCircleHtml('w-5 h-5 text-green-500', null, null); ?>
                                        </div>
                                        <div class="ml-2 mw-review-pros-content">
                                            <?= /* @noEscape */ nl2br($escaper->escapeHtml($review->getPros())) ?>
                                        </div>
                                    </div>
                                    <div class="flex items-start md:w-1/2 mw-review-cons">
                                        <div>
                                            <?= $heroicons->minusCircleHtml('w-5 h-5 text-red-400', null, null); ?>
                                        </div>
                                        <div class="ml-2 mw-review-cons-content">
                                            <?= /* @noEscape */ nl2br($escaper->escapeHtml($review->getCons())) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- pros and cons -->
                        <?php endif; ?>

                        <?php if ($block->isDisplayHelpful()): ?>
                            <script>
                                function initMwVotes(reviewId){
                                    const voteConfig = <?= $block->getJsonVoteConfig() ?>;
                                    const [INIT, LIKE, DISLIKE] = ["init", "like", "dislike"];

                                    return {
                                        isLoading: true,
                                        dislikes: 0,
                                        likes: 0,
                                        choosen: null,
                                        init() {
                                            this.fetchVote(INIT);
                                        },
                                        addLike() {
                                            this.fetchVote(LIKE);
                                        },
                                        addDislike() {
                                            this.fetchVote(DISLIKE);
                                        },
                                        fetchVote(action) {
                                            this.isLoading = true;

                                            const url = new URL(voteConfig.ajaxUrl);
                                            url.searchParams.append("uniq", Date.now());
                                            url.searchParams.append("id", reviewId);
                                            url.searchParams.append("form_key", hyva.getFormKey());
                                            url.searchParams.append("action", action);

                                            fetch(url, {
                                                method: 'GET',
                                                headers: {
                                                    "X-Requested-With": "XMLHttpRequest",
                                                }
                                            })
                                                .then(response => {
                                                    if (! response.ok) console.warn('request failed');
                                                    return response.json()
                                                })
                                                .then(data => {
                                                    if (data.overall_data) {
                                                        const {like_count, dislike_count} = data.overall_data;
                                                        this.likes = like_count;
                                                        this.dislikes = dislike_count;
                                                    }
                                                    if (data.personal_data) {
                                                        const {like_count, dislike_count} = data.personal_data;
                                                        if (+like_count) {
                                                            this.choosen = "like";
                                                        }
                                                        else if (+dislike_count) {
                                                            this.choosen = "dislike";
                                                        }
                                                    }
                                                })
                                                .catch(error => {
                                                    console.log(error)
                                                })
                                                .finally(() => {
                                                    this.isLoading = false;
                                                })
                                        },
                                    }
                                }
                            </script>

                            <!-- is helpful -->
                            <div class="mt-4 inline-flex items-center text-gray-500 mw-review-helpful"
                                 x-data="initMwVotes(<?= (int)$review->getId() ?>)" x-init="init()">
                                <div class="mw-review-helpful-label">
                                    <?= $escaper->escapeHtml(__('Review helpful?')) ?>
                                </div>
                                <div class="ml-2 inline-flex items-start mw-review-helpful-like">
                                    <button aria-label="Like"
                                            :class="{'text-green-500 hover:text-green-500': choosen === 'like' }"
                                            :class="{'text-gray-400 hover:text-gray-600': choosen !== 'like' }"
                                            :disabled="choosen === 'like' || isLoading" @click="addLike()">
                                        <?= $heroicons->thumbUpHtml('w-5 h-5 ml-0.5', null, null); ?>
                                    </button>
                                    <div class="ml-0.5" x-text="likes"></div>
                                </div>
                                <div class="ml-2 inline-flex items-center mw-review-helpful-dislike">
                                    <button aria-label="Dislike"
                                            :class="{'text-red-500 hover:text-red-500': choosen === 'dislike' }"
                                            :class="{'text-gray-400 hover:text-gray-600': choosen !== 'dislike' }"
                                            :disabled="choosen === 'dislike' || isLoading" @click="addDislike()">
                                        <?= $heroicons->thumbDownHtml('w-5 h-5 ml-0.5', null, null); ?>
                                    </button>
                                    <div class="ml-0.5" x-text="dislikes"></div>
                                </div>
                                <div x-show="isLoading">
                                    <div style="border-top-color:transparent"
                                         class="ml-2 w-4 h-4 border-4 border-blue-400 border-solid rounded-full animate-spin"></div>
                                </div>
                            </div>
                            <!-- is helpful -->
                        <?php endif; ?>

                        <?php if ($review->getAnswer()): ?>
                            <!-- review answer -->
                            <div class="mt-8 mw-review-answer">
                                <div class="text-lg mw-review-answer-label">
                                    <?= $escaper->escapeHtml(__('Brand reply')) ?>
                                </div>
                                <div class="mt-2 mw-review-answer-content">
                                    <?= /* @noEscape */ nl2br($escaper->escapeHtml($review->getAnswer())) ?>
                                </div>
                            </div>
                        <?php endif; ?>

                    </div>
                <?php endforeach; ?>
            </div>
            <!-- Reviews -->
        </div>

        <div class="mt-4 mw-reviews-toolbar">
            <?= $block->getToolbar() ? $block->getToolbar()->getPagerHtml() : '' ?>
        </div>

        <!-- images gallery -->
        <div x-cloak class="w-full h-full fixed block top-0 left-0 bg-white z-50 flex"
             x-show="gallery.currentImage.isShow"
             x-spread="galleryEventListeners">
            <div class="absolute top-0 right-0 pt-4 pr-4">
                <button type="button" @click="gallery.currentImage.isShow = false"
                        class="text-gray-400 hover:text-gray-500 focus:outline-none focus:text-gray-500 transition ease-in-out duration-150" aria-label="Close">
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="absolute z-10 top-1/2 left-0 bg-gray-100 hover:bg-gray-300 opacity-50">
                <button class="p-8 opacity-100" type="button" @click="gallery.openPrevImage()"
                        class="text-gray-400 hover:text-gray-500 focus:outline-none focus:text-gray-500 transition ease-in-out duration-150" aria-label="Prev">
                    <?= $heroicons->arrowLeftHtml('w-10 h-10', null, null) ?>
                </button>
            </div>
            <div class="absolute z-10 top-1/2 right-0 bg-gray-100 hover:bg-gray-300 opacity-50">
                <button class="p-8 opacity-100" type="button" @click="gallery.openNextImage()"
                        class="text-gray-400 hover:text-gray-500 focus:outline-none focus:text-gray-500 transition ease-in-out duration-150" aria-label="Next">
                    <?= $heroicons->arrowRightHtml('w-10 h-10', null, null) ?>
                </button>
            </div>
            <div class="relative self-center w-full"
                 x-transition:enter="ease-out duration-500"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100">
                <div class="relative mb-6">
                    <img :alt="gallery.currentImage.label"
                         :title="gallery.currentImage.label"
                         class="absolute inset-0 object-contain object-center w-full m-auto max-h-screen-75"
                         :class="'opacity-0'"
                         @click="gallery.currentImage.isShow = true"
                         width="700"
                         height="700"
                         :src="gallery.currentImage.orig"/>
                    <template x-for="(image, index) in gallery.images" :key="index">
                        <img
                            :alt="gallery.currentImage.label"
                            :title="gallery.currentImage.label"
                            class="absolute inset-0 object-contain object-center w-full m-auto max-h-screen-75"
                            :class="{ 'cursor-pointer': !gallery.currentImage.isShow }"
                            width="700"
                            height="700"
                            :loading="gallery.currentImage.index!==index ? 'lazy' : 'eager'"
                            :src="gallery.currentImage.isShow ? image.orig : image.thumb"
                            x-show.transition.opacity.duration.500ms="gallery.currentImage.index===index"
                        />
                    </template>
                </div>
            </div>
            <div id="thumbs" class="flex flex-wrap"
                 :class="{ 'fixed justify-center bottom-0 left-0 right-0 mx-6': gallery.currentImage.isShow }"
                 style="min-height: 100px;">
                <template x-for="(image, index) in gallery.images" :key="index">
                    <div class="mb-2 mr-2 lg:mr-4 last:mr-0">
                        <a href="#" @click.prevent="gallery.openImage(image.orig);"
                           class="block border border-gray-300 hover:border-primary focus:border-primary"
                           :class="{'border-primary': gallery.currentImage.index===index}">
                            <img :src="image.thumb"
                                 :alt="image.label"
                                 :title="image.label"
                                 width="90"
                                 height="90"
                            />
                        </a>
                    </div>
                </template>
            </div>
        </div>
    </div>
    <!-- images gallery -->

<?php endif; ?>
