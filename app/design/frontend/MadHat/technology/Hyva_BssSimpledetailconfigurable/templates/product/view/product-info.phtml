<?php
/**
 * BSS Commerce Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://bsscommerce.com/Bss-Commerce-License.txt
 *
 * @category  BSS
 * @package   Hyva_BssSimpledetailconfigurable
 * <AUTHOR> Team
 * @copyright Copyright (c) 2023 BSS Commerce Co. ( http://bsscommerce.com )
 * @license   http://bsscommerce.com/Bss-Commerce-License.txt
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\BssSimpledetailconfigurable\ViewModel\Helper as ModuleViewModel;
use MadHat\Catalog\ViewModel\MadHatCatalogConfig;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var Product $product */
$product = $productViewModel->getProduct();

/** @var MadHatCatalogConfig $madhatCatalogViewModel */
$madhatCatalogViewModel = $viewModels->require(MadHatCatalogConfig::class);

$taxDisplayType = $madhatCatalogViewModel->getTaxDisplayType();

$moduleViewModel = $viewModels->require(ModuleViewModel::class);
$moduleConfig = $moduleViewModel->getModuleConfig();

if($product->getTypeId() == "configurable") {
    $jsonChildProductData = $moduleViewModel->getJsonChildProductData($product->getId());
    $childProductData = json_decode($jsonChildProductData, true);
}
?>
<div class="w-full mb-6">
    <div class="pdp-article-ean-out">
        <div class="sku-col">
            <span class=""><?= __('SKU') ." :" ?></span>
            <?php if (isset($childProductData['child'])) : ?>
                <span class="font-medium" x-data="{sku: '<?= $escaper->escapeJs($product->getSku()) ?>'}" x-text="sku"
                      @simple-detail-product-active.window="sku = $event.detail.product.sku">
                    <?= $product->getSku() ?>
                </span>
                <span class="hidden"><?= $product->getProductUrl() ?></span>
            <?php else: ?>
                <span class="font-medium" itemprop="sku" x-data="{sku: '<?= $escaper->escapeJs($product->getSku()) ?>'}" x-text="sku"
                      @simple-detail-product-active.window="sku = $event.detail.product.sku">
                    <?= $product->getSku() ?>
                </span>
                <span itemprop="url" class="hidden"><?= $product->getProductUrl() ?></span>
            <?php endif; ?>
        </div>
        <?php $productEan = $product->getMadhatEan(); ?>
        <div class="ean-col">
            <p class="flex gap-2" x-data="{madhat_ean: '<?= $productEan ?>'}" x-show="madhat_ean.length > 0 && madhat_ean > 0">
                <span class=""><?= __('EAN')  ." :" ?></span>
                <?php if (isset($childProductData['child'])) : ?>
                <span class="font-medium" x-text="madhat_ean" @simple-detail-product-active.window="madhat_ean = $event.detail.product.madhat_ean">
                <?php else: ?>
                    <span class="font-medium" itemprop="gtin" x-text="madhat_ean" @simple-detail-product-active.window="madhat_ean = $event.detail.product.madhat_ean">
                <?php endif; ?>
                <?= $productEan ?>
                </span>
            </p>
        </div>
        <?php $productManufacturerSku = $product->getMadhatManufacturerNumber(); ?>
        <div class="manufacturer-number">
            <p class="flex gap-2" x-data="{madhat_manufacturer_number: '<?= $escaper->escapeJs($productManufacturerSku) ?>'}" x-show="madhat_manufacturer_number.length > 0">
                <span class=""><?= __('Mfg Part No') ." :" ?></span>
                <span class="font-medium" x-text="madhat_manufacturer_number"
                        @simple-detail-product-active.window="madhat_manufacturer_number = $event.detail.product.madhat_manufacturer_number">>
                        <?= $productManufacturerSku ?>
                    </span>
            </p>
        </div>
    </div>
    <?php if ($shortDescription = $productViewModel->getShortDescription(false, false)) { ?>
        <?php if ($moduleConfig->isShowDescription() && $moduleConfig->isModuleEnable()): ?>
            <script>
                function initSimpleShortDescription() {
                    return {
                        shortDescription:"<?= $escaper->escapeJs($shortDescription) ?>",
                        eventListeners: {
                            ['@simple-detail-product-active.window']($event) {
                                if ($event.detail.product.sdesc) {
                                    this.shortDescription = $event.detail.product.sdesc;
                                } else {
                                    this.shortDescription =  $event.detail.product.desc;
                                }
                            }
                        }

                    }
                }
            </script>
        <?php if (isset($childProductData['child'])) : ?>
            <div class="py-4 mb-4 leading-relaxed product-description border-b border-cgrey-75"
                 x-data="initSimpleShortDescription()" x-bind="eventListeners">
                <div>
                    <div x-html="shortDescription"><?= /* @noEscape */ $shortDescription ?></div>
                </div>
            </div>
        <?php else: ?>
            <div itemprop="description" class="py-4 mb-4 leading-relaxed product-description border-b border-cgrey-75"
                 x-data="initSimpleShortDescription()" x-bind="eventListeners">
                <div>
                    <div x-html="shortDescription"><?= /* @noEscape */ $shortDescription ?></div>
                </div>
            </div>
        <?php endif; ?>
        <?php else: ?>
        <?php if (isset($childProductData['child'])) : ?>
            <div class="py-4 mb-4 leading-relaxed product-description border-b border-cgrey-75"><?= /* @noEscape */ $shortDescription ?></div>
        <?php else: ?>
            <div itemprop="description" class="py-4 mb-4 leading-relaxed product-description border-b border-cgrey-75"><?= /* @noEscape */ $shortDescription ?></div>
        <?php endif; ?>
        <?php endif; ?>

    <?php } ?>

    <?php if ($moduleConfig->isShowTierPrice() && $tierPriceBlock = $block->getChildHtml("product.price.tier")): ?>
        <div class="tier-price-container">
            <?= /** @noEscape */ $tierPriceBlock ?>
        </div>
    <?php endif; ?>

    <?= $block->getChildHtml('product.info.form') ?>

    <div class="flex flex-wrap lg:flex-nowrap mb-4 justify-between sm:justify-start py-6 border-cgrey-75 border-b">
        <div class="price-vat-stock-container">
            <div class="price-section">
                <div role="group" aria-label="<?= $escaper->escapeHtmlAttr('Price') ?>">
                    <?= $block->getChildHtml("product.info.price") ?>
                </div>
            </div>
            <div class="vat-message">
                <?php if ($taxDisplayType == 1): ?>
                    <?= __('Excl. VAT excl. Delivery') ?>
                <?php else: ?>
                    <?= __('Incl. VAT excl. Delivery') ?>
                <?php endif; ?>
            </div>

            <?= $block->getChildHtml("product.info.stockstatus") ?>
        </div>
        <div>
                <?= $block->getChildHtml("product.info.quantity") ?>
        </div>
        <div>
                <?= $block->getChildHtml("product.info.addtocart") ?>
        </div>
        <div>
            <?= $block->getChildHtml('product.info.addtowishlist'); ?>
        </div>
    </div>

    <?= $block->getChildHtml("product.info.additional") ?>
</div>
