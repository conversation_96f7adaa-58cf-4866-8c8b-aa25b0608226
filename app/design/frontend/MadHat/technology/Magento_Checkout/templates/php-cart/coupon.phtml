<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Checkout\Block\Cart\Coupon;
use Magento\Framework\Escaper;

/** @var Coupon $block */
/** @var Escaper $escaper */

// We should use strlen function because coupon code could be "0", converted to bool will lead to false
$hasCouponCode = (bool) strlen($block->getCouponCode() ?: "");
?>
<script>
    function initCouponForm() {
        return {
            showCouponForm: <?= $hasCouponCode ? 1 : 0 ?>,
            formData: {
                coupon_code: '<?= $escaper->escapeJs($block->getCouponCode()) ?>',
                remove: '<?= (int) $hasCouponCode ?>'
            },
            init(){
                this.showCouponForm = JSON.parse(hyva.getBrowserStorage().getItem('hyva.showCouponForm'))
            },
            toggleShowCoupon(){
                this.showCouponForm = !this.showCouponForm;

                hyva.getBrowserStorage().setItem('hyva.showCouponForm', this.showCouponForm);

                this.$nextTick(() => this.$refs.couponInput.select());
            }
        }
    }
</script>
<div class="coupon-form py-4 border-b border-t border-container"
     x-data="initCouponForm()"
>
    <div class="mx-auto md:mx-0">
        <div class="text-left">
            <button
                @click="toggleShowCoupon()"
                class="inline-block cursor-pointer text-primary-lighter select-none whitespace-nowrap"
                id="discount-form-toggle"
                :aria-expanded="showCouponForm"
                type="button"
            >
                <span>
                    <?= $escaper->escapeHtml(__('Apply Discount Code')) ?>
                </span>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    class="w-6 h-6 inline-block"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    role="presentation"
                    focusable="false"
                >
                    <path x-show="!showCouponForm" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 9l-7 7-7-7"/>
                    <path
                        x-show="showCouponForm" x-cloak stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M5 15l7-7 7 7"/>
                </svg>
            </button>
        </div>
        <div>
            <form id="discount-coupon-form"
                  class="mt-4 mb-2"
                  x-show="showCouponForm"
                  x-cloak
                  action="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/couponPost')) ?>"
                  method="post"
                  @submit.prevent="hyva.postCart($event.target)"
            >
                <?= $block->getBlockHtml('formkey') ?>

                <div class="fieldset coupon">

                    <input type="hidden" name="remove" id="remove-coupon" value="<?= (int) $hasCouponCode ?>"/>

                    <div class="flex flex-col px-7 sm:px-0 sm:flex-row gap-2 sm:gap-6 justify-center sm:justify-start">

                        <label for="coupon_code" class="label sr-only">
                            <?= $escaper->escapeHtml(__('Enter discount code')) ?>
                        </label>
                        <div class="control">
                            <input type="text"
                                   class="form-input w-full sm:w-auto rounded-md disabled:opacity-75 disabled:bg-gray-100 disabled:pointer-events-none"
                                   id="coupon_code"
                                   name="coupon_code"
                                   value="<?= $escaper->escapeHtmlAttr($block->getCouponCode()) ?>"
                                   x-model="formData.coupon_code"
                                   x-ref="couponInput"
                                   placeholder="<?= $escaper->escapeHtmlAttr(__('Enter discount code')) ?>"
                                    <?php if ($hasCouponCode): ?>
                                       disabled="disabled"
                                    <?php else: ?>
                                        required
                                    <?php endif; ?>
                            />
                        </div>

                        <div>
                            <?php if (!$hasCouponCode): ?>
                            <div class="primary">
                                <button class="btn btn-primary py-2.5 w-full sm:w-auto justify-center" type="submit" value="<?= $escaper->escapeHtmlAttr(__('Apply Discount')) ?>">
                                    <span><?= $escaper->escapeHtml(__('Apply Discount')) ?></span>
                                </button>
                            </div>
                            <?php else: ?>
                                <div class="primary">
                                    <button type="submit" class="btn btn-primary py-2.5 width-full sm:width-auto"
                                            value="<?= $escaper->escapeHtmlAttr(__('Cancel Coupon')) ?>">
                                        <?= $escaper->escapeHtml(__('Cancel Coupon')) ?>
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

