<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Magewire\Checkout\TermsConditions;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\Model\ViewModelRegistry;

/** @var Template $block */
/** @var HeroiconsOutline $iconsViewModel */
/** @var TermsConditions $magewire */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

$iconsViewModel = $viewModels->require(HeroiconsOutline::class);
$availableTerms = $block->getData('terms');
$heroIcons = $viewModels->require(HeroiconsSolid::class);
?>

<?php if (is_array($availableTerms) && count($availableTerms) !== 0): ?>
<div class="space-y-4">
    <?php foreach ($availableTerms as $agreement): ?>
        <?php $dialogName = 'agreement_' . $agreement->getAgreementId();  ?>

        <div x-data="{ open: false, hasError: false }" x-on:quote:actions:error.window="hasError = ('terms' in $event.detail) && $event.detail.terms.includes(<?= $escaper->escapeJs($agreement->getAgreementId()) ?>)">
            <?php // For js implementation check Hyva_Checkout/templates/page/js/api/v1/init-term-and-condition-modal.phtml template ?>
            <div x-data="initTermAgreement" data-agreement-id="<?= $agreement->getAgreementId() ?>" id="<?= $dialogName ?>">
                <div class="flex gap-x-4">
                    <div class="flex items-center">
                        <input type="checkbox"
                            class="flex-none disabled:opacity-25"
                            id="agreement_<?= $escaper->escapeHtmlAttr($agreement->getAgreementId()) ?>"
                            value="<?= $escaper->escapeHtmlAttr($agreement->getMode()) ?>"
                            wire:model="termAcceptance.<?= $escaper->escapeHtmlAttr($agreement->getAgreementId()) ?>"
                            required
                            x-on:input="hasError = hasError && ! $event.target.checked"
                        />
                    </div>

                    <div class="flex-1 gap-y-2">
                        <div class="flex justify-between gap-x-4">
                            <label for="agreement_<?= $escaper->escapeHtmlAttr($agreement->getAgreementId()) ?>">
                                <span class="font-bold" x-bind:class="{ 'text-red-600': hasError, 'text-gray-700': ! hasError }">
                                    <?= /* @noEscape */ $agreement->getCheckboxText() ?>
                                </span>
                            </label>
                        </div>
                    </div>
                </div>

                <div
                    class="fixed w-full h-full top-0 left-0 z-30 bg-black/50"
                    x-cloak
                    x-bind="overlay('<?= $escaper->escapeJs($dialogName) ?>')"
                    x-spread="overlay('<?= $escaper->escapeJs($dialogName) ?>')"
                >
                    <div class="fixed flex justify-center items-center text-left z-10 inset-0">
                        <div
                            role="dialog"
                            class="relative inline-block bg-white shadow-xl overflow-auto rounded-xl m-4 dialog-height"
                            aria-labelledby="<?= $escaper->escapeHtmlAttr($dialogName) ?>-label"
                            aria-modal="true"
                            x-ref="<?= $escaper->escapeHtmlAttr($dialogName) ?>"
                        >
                            <button
                                class="close-icon"
                                x-focus-first
                                @click="hide"
                                area-label="close modal popup"
                                title="close"
                            >
                                <i class="fa-regular fa-xmark"></i>
                            </button>
                            <div class="p-7 flex flex-col lg:flex-row lg:gap-5">
                                <div class="text-center lg:text-left">
                                    <div
                                        id="<?= $escaper->escapeHtmlAttr($dialogName) ?>-label"
                                        class="text-xl leading-7 font-bold text-gray-800 mb-3"
                                    >
                                        <?= $escaper->escapeHtml(__('Agreement')) ?>
                                    </div>
                                    <div class="text-gray-600">
                                        <?php if ($agreement->getIsHtml()): ?>
                                            <?= /* @noEscape */ $agreement->getContent() ?>
                                        <?php else: ?>
                                            <p>
                                                <?= $escaper->escapeHtml($agreement->getContent()) ?>
                                            </p>
                                        <?php endif ?>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

        </div>
    <?php endforeach ?>
</div>
<?php endif ?>
