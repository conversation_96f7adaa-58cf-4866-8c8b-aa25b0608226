<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Request a Quote Hyva Compatibility
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

$storeConfig = $viewModels->require(StoreConfig::class);
$maxItemsToDisplay = $storeConfig->getStoreConfig('checkout/sidebar/max_items_display_count');

/** @var HeroiconsOutline $heroIcons */
$heroIcons = $viewModels->require(HeroiconsOutline::class);
?>
<script>
    function initAmQuoteCartDrawer() {
        return {
            open: false,
            isLoading: false,
            quotecart: {},
            maxItemsToDisplay: <?= (int)$maxItemsToDisplay ?>,
            itemsCount: 0,
            totalCartAmount: 0,
            getData(data) {
                if (data.quotecart) {
                    this.quotecart = data.quotecart;
                    this.itemsCount = data.quotecart.items && data.quotecart.items.length || 0;
                    this.totalCartAmount = this.quotecart.summary_count;
                    this.setQuoteItems();
                }
                this.isLoading = false;
            },
            quoteItems: [],
            getItemCountTitle() {
                return hyva.strf('(%0 <?= $escaper->escapeJs(__('of')) ?> %1)', this.maxItemsToDisplay, this.itemsCount)
            },
            setQuoteItems() {
                this.quoteItems = this.quotecart.items && this.quotecart.items.sort((a, b) => b.item_id - a.item_id) || [];

                if (this.maxItemsToDisplay > 0) {
                    this.quoteItems = this.quoteItems.slice(0, parseInt(this.maxItemsToDisplay, 10));
                }
            },
            deleteItemFromQuote(itemId) {
                this.isLoading = true;

                const formKey = hyva.getFormKey();
                const postUrl = BASE_URL + 'amasty_quote/sidebar/removeItem/';

                fetch(postUrl, {
                    "headers": {
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                    "body": "form_key=" + formKey + "&item_id=" + itemId,
                    "method": "POST",
                    "mode": "cors",
                    "credentials": "include"
                }).then(response => {
                    if (response.redirected) {
                        window.location.href = response.url;
                    } else if (response.ok) {
                        return response.json();
                    } else {
                        window.dispatchMessages && window.dispatchMessages([{
                            type: 'warning',
                            text: '<?= $escaper->escapeJs(__('Could not remove item from quote.')) ?>'
                        }]);
                        this.isLoading = false;
                    }
                }).then(result => {
                    window.dispatchMessages && window.dispatchMessages([{
                        type: result.success ? 'success' : 'error',
                        text: result.success
                            ? '<?= $escaper->escapeJs(__('You removed the item.')) ?>'
                            : result.error_message
                    }], result.success ? 5000 : 0)
                    window.dispatchEvent(new CustomEvent('reload-customer-section-data'));
                });
            }
        }
    }
</script>
<section id="quotecart-drawer"
         x-data="initAmQuoteCartDrawer()"
         @private-content-loaded.window="getData($event.detail.data)"
         @toggle-amquote.window="open=true"
         @keydown.window.escape="open=false"
>
    <template x-if="quotecart">
        <div role="dialog"
             aria-labelledby="cart-drawer-title"
             aria-modal="true"
             @click.away="open=false"
             class="fixed inset-y-0 right-0 z-40 flex max-w-full">
            <div class="backdrop"
                 x-show="open"
                 x-transition:enter="ease-in-out duration-500"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="ease-in-out duration-500"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 @click="open=false"
                 aria-label="Close panel"></div>
            <div class="relative w-screen max-w-md shadow-2xl"
                 x-show="open"
                 x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
                 x-transition:enter-start="translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="translate-x-full"
            >
                <div
                    x-show="open"
                    x-transition:enter="ease-in-out duration-500"
                    x-transition:enter-start="opacity-0"
                    x-transition:enter-end="opacity-100"
                    x-transition:leave="ease-in-out duration-500"
                    x-transition:leave-start="opacity-100"
                    x-transition:leave-end="opacity-0" class="absolute top-0 right-0 flex p-2 mt-2">
                    <button @click="open=false" aria-label="Close panel"
                            class="p-2 text-gray-300 transition duration-150 ease-in-out hover:text-black">
                        <?= $heroIcons->xHtml(); ?>
                    </button>
                </div>
                <div class="flex flex-col h-full py-6 space-y-6 bg-white shadow-xl">
                    <?= $block->getChildHtml('quotecart-drawer.top'); ?>

                    <header class="px-4 sm:px-6">
                        <h2 id="cart-drawer-title" class="text-lg font-medium leading-7 text-gray-900">
                            <span class="font-semibold"><?= $escaper->escapeHtml(__('Quote Summary')) ?></span>
                            <span class="items-total text-xs"
                                  x-show="maxItemsToDisplay && maxItemsToDisplay < itemsCount"
                                  x-text="getItemCountTitle()"></span>
                        </h2>
                    </header>

                    <?= $block->getChildHtml('quotecart-drawer.items.before'); ?>

                    <div x-show="itemsCount" class="relative grid gap-6 px-4 py-6 overflow-y-auto bg-white border-b
                        sm:gap-8 sm:px-6 border-container">
                        <template x-for="item in quoteItems">
                            <div class="flex items-start p-3 -m-3 space-x-4">
                                <a :href="item.product_url" class="w-1/4">
                                    <img
                                        :src="item.product_image.src"
                                        :width="item.product_image.width"
                                        :height="item.product_image.height"
                                        loading="lazy"
                                    />
                                </a>
                                <div class="w-3/4 space-y-2">
                                    <div>
                                        <p class="font-semibold">
                                            <span x-html="item.qty"></span> x <span x-html="item.product_name"></span>
                                        </p>
                                        <!-- <p class="text-sm"><span x-html="item.product_sku"></span></p> -->
                                    </div>
                                    <div class="product-item-attributes text-sm">
                                        <template x-for="option in item.options">
                                            <span class="break-words inline-block" x-html="option.value"></span>
                                        </template>
                                    </div>
                                    <p><span x-html="item.product_price" class="font-semibold"></span></p>
                                    <div>
                                        <a :href="item.configure_url"
                                           x-show="item.product_type !== 'grouped'"
                                           class="inline-flex py-1 px-2 mr-2 btn btn-primary">
                                            <?= $heroIcons->pencilHtml('', 20, 20); ?>
                                        </a>
                                        <button class="inline-flex py-1 px-2 btn btn-primary"
                                                @click="deleteItemFromQuote(item.item_id)">
                                            <?= $heroIcons->trashHtml('', 20, 20); ?>
                                        </button>
                                    </div>

                                </div>
                            </div>
                        </template>
                    </div>

                    <?= $block->getChildHtml('quotecart-drawer.totals.before'); ?>

                    <div x-show="itemsCount" class="relative grid gap-6 px-4 py-6 bg-white sm:gap-8 sm:px-6">
                        <div class="w-full p-3 -m-3 space-x-4">
                            <p class="font-semibold text-lg"><?= $escaper->escapeHtml(__('Subtotal')) ?>: <span x-html="quotecart.subtotal"></span></p>
                        </div>
                        <div class="flex justify-center w-full">
                            <a @click.prevent.stop="$dispatch('toggle-authentication',
                                {url: '<?= $escaper->escapeUrl($block->getUrl('amasty_quote/cart')) ?>'});"
                               href="<?= $escaper->escapeUrl($block->getUrl('amasty_quote/cart')) ?>"
                               class="w-full btn btn-primary justify-center lg:h-[50px]">
                                <?= $escaper->escapeHtml(__('My Quote Cart')) ?>
                            </a>
                        </div>
                        <?= $block->getChildHtml('extra_actions') ?>
                    </div>

                    <template x-if="!itemsCount">
                        <div class="relative px-4 py-6 bg-white border-bs sm:px-6 border-container">
                            <?= $escaper->escapeHtml(__('Quote is empty')) ?>
                        </div>
                    </template>

                    <?= $block->getChildHtml('quotecart-drawer.bottom'); ?>
                </div>
            </div>
            <?= $block->getChildHtml('loading') ?>
        </div>
    </template>
</section>
