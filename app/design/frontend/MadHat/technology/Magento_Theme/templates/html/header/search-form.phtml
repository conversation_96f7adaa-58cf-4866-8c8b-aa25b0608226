<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Search\Helper\Data as SearchHelper;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\Model\ViewModelRegistry;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var SearchHelper $helper */

$helper = $this->helper(SearchHelper::class);

/** @var HeroiconsOutline $heroIcons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<script>
    'use strict';

    function initMiniSearch() {
        return {
            minSearchLength: <?= (int) $helper->getMinQueryLength() ?>,
            suggestions: [],
            suggest() {
                const search = this.$refs.searchInput;
                if (search.value.length >= this.minSearchLength) {
                    search.setCustomValidity('');
                    search.reportValidity();
                    this.fetchSuggestions(search.value);
                } else {
                    this.suggestions = [];
                }
            },
            fetchSuggestions(term) {
                fetch(
                    window.BASE_URL + 'search/ajax/suggest?' + new URLSearchParams({q: term}),
                    {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    }
                )
                .then(response => response.json())
                .then(result => this.suggestions = result);
            },
            search(term) {
                const search = this.$refs.searchInput;
                term = term || search.value;
                if (term.length < this.minSearchLength) {
                    search.setCustomValidity('<?= $escaper->escapeJs(
                        __('Minimum Search query length is %1', $helper->getMinQueryLength())
                    ) ?>');
                    search.reportValidity();
                } else {
                    search.setCustomValidity('');
                    search.value = term;
                    this.$refs.form.submit();
                }
            },
            focusElement(element) {
                if (element && element.nodeName === "DIV") {
                    element.focus();
                    return true;
                } else {
                    return false;
                }
            }
        }
    }
</script>
<search x-data="initMiniSearch()">
     <form class="form minisearch z-0" id="search_mini_form" x-ref="form" @submit.prevent="search()"
          action="<?= $escaper->escapeUrl($helper->getResultUrl()) ?>" method="get">
        <div class="flex">
            <label class="sr-only" for="search" data-role="minisearch-label">
                <span><?= $escaper->escapeHtml(__('Search')) ?></span>
            </label>
            <input id="search"
                   x-ref="searchInput"
                   type="search"
                   autocomplete="off"
                   name="<?= $escaper->escapeHtmlAttr($helper->getQueryParamName()) ?>"
                   value="<?= /** @noEscape */ $helper->getEscapedQueryText() ?>"
                   placeholder="<?= $escaper->escapeHtmlAttr(__('Search entire store here...')) ?>"
                   maxlength="<?= $escaper->escapeHtmlAttr($helper->getMaxQueryLength()) ?>"
                   class="w-full border-slate-300 rounded-l-md"
                   @focus.once="suggest"
                   @input.debounce.300="suggest"
                   @keydown.arrow-down.prevent="focusElement($root.querySelector('[tabindex]'))"
                   @search-open.window.debounce.10="$el.focus(); $el.select()"
            >
            <button
                type="submit"
                title="<?= $escaper->escapeHtml(__('Search')) ?>"
                class="action search btn justify-center rounded-l-none rounded-r-md px-3 text-base shadow-none hover:shadow-lg active:shadow disabled:shadow-none transition
                    bg-blue-600 text-white border border-transparent hover:bg-blue-700 focus:ring-blue-200 active:bg-blue-700 disabled:bg-slate-600 disabled:text-slate-50 disabled:opacity-70"
                aria-label="<?= $escaper->escapeHtml(__('Search')) ?>"
            >
                <?= $heroicons->searchHtml('', 24, 24, ['aria-hidden' => 'true']); ?>
            </button>
        </div>
        <template x-if="suggestions.length > 0">
            <div class="w-full leading-normal text-slate-800 flex flex-col mt-2">
                <template x-for="suggestion in suggestions">
                    <div class="flex justify-between p-2 bg-container-lighter even:bg-container mb-1 cursor-pointer
                                 border border-container hover:bg-container-darker rounded-md"
                          tabindex="0"
                          @click="search(suggestion.title)"
                          @keydown.enter="search(suggestion.title)"
                          @keydown.arrow-up.prevent="
                              focusElement($event.target.previousElementSibling) || $refs.searchInput.focus()
                          "
                          @keydown.arrow-down.prevent="focusElement($event.target.nextElementSibling)"
                    >
                        <span x-text="suggestion.title"></span>
                        <span x-text="suggestion.num_results"></span>
                    </div>
                </template>
            </div>
        </template>
    </form>
</search>
