<?php
declare(strict_types=1);

namespace Make<PERSON><PERSON>\CacheAnalyst\Cron;

use Magento\Framework\Exception\LocalizedException;
use Makewe<PERSON>\CacheAnalyst\Helper\AnalystConfig;
use Make<PERSON><PERSON>\CacheAnalyst\Model\ResourceModel\CacheAnalyst as CacheAnalystResource;

class CacheAnalystCleanLog
{
    /**
     * @var AnalystConfig
     */
    private AnalystConfig $analystConfig;

    /**
     * @var CacheAnalystResource
     */
    private CacheAnalystResource $cacheAnalystResource;

    public function __construct(
        AnalystConfig $analystConfig,
        CacheAnalystResource $cacheAnalystResource
    )
    {
        $this->analystConfig = $analystConfig;
        $this->cacheAnalystResource = $cacheAnalystResource;
    }

    /**
     *
     * @return void
     * @throws LocalizedException
     */
    public function execute(): void
    {
        $this->cacheAnalystResource->deleteCacheLogBeforeDays(
            (int) $this->analystConfig->getCleanLogBeforeDays()
        );
    }
}
