<?php

namespace <PERSON><PERSON><PERSON>\CacheAnalyst\Controller\Adminhtml\Logs;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\ResultFactory;
use Magento\Ui\Component\MassAction\Filter;
use Makewebo\CacheAnalyst\Model\CacheAnalystFactory;
use Makewebo\CacheAnalyst\Model\CacheAnalyst;
use Makewebo\CacheAnalyst\Model\ResourceModel\CacheAnalyst\CollectionFactory;
use Makewebo\CacheAnalyst\Model\ResourceModel\CacheAnalyst\Collection;

class MassDelete extends Action
{


    public $collectionFactory;

    public $filter;

    protected $cacheAnalystFactory;

    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory,
        CacheAnalystFactory $cacheAnalystFactory
    ) {
        $this->filter = $filter;
        $this->collectionFactory = $collectionFactory;
        $this->cacheAnalystFactory = $cacheAnalystFactory;
        parent::__construct($context);
    }

    public function execute()
    {
        try {
            /** @var Collection $collection */
            $collection = $this->filter->getCollection($this->collectionFactory->create());

            $count = 0;
            /** @var CacheAnalyst $model */
            foreach ($collection as $model) {
                if ($model->getId()) {
                    $model = $this->cacheAnalystFactory->create()->load($model->getId());
                    $model->delete();
                    $count++;
                }
            }
            $this->messageManager->addSuccess(__('A total of %1 log(s) have been deleted.', $count));
        } catch (\Exception $e) {
            $this->messageManager->addError(__($e->getMessage()));
        }
        return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setPath('*/*/index');
    }

}
