<?php

namespace MadHat\SiteIntegrationShipping\Block\Tracking;

use Magento\Framework\App\ObjectManager;

class Popup extends \Magento\Shipping\Block\Tracking\Popup
{
    /**
     * Get description for current tracking
     *
     * This method retrieves the description from the sales_shipment_track.description column
     * which typically contains tracking URLs or additional tracking information
     *
     * @return string|null
     */
    public function getTrackingDescription()
    {
        $track = $this->getData('track');

        // Handle track object (most common case)
        if (is_object($track)) {
            // Try getDescription() method first
            if (method_exists($track, 'getDescription')) {
                $description = $track->getDescription();
                if (!empty($description)) {
                    return $description;
                }
            }

            // Try getData('description') method
            if (method_exists($track, 'getData')) {
                $description = $track->getData('description');
                if (!empty($description)) {
                    return $description;
                }
            }
        }

        // Handle track array - load from database since array doesn't contain description
        if (is_array($track) && isset($track['number'])) {
            $trackNumber = $track['number'];
            $description = $this->getDescriptionFromDatabase($trackNumber);
            if (!empty($description)) {
                return $description;
            }
        }

        // Handle track array (fallback case)
        if (is_array($track) && isset($track['description'])) {
            $description = $track['description'];
            return !empty($description) ? $description : null;
        }

        return null;
    }

    /**
     * Get description from database using track number
     *
     * @param string $trackNumber
     * @return string|null
     */
    private function getDescriptionFromDatabase($trackNumber)
    {
        try {
            $objectManager = ObjectManager::getInstance();
            $resourceConnection = $objectManager->get(\Magento\Framework\App\ResourceConnection::class);

            $connection = $resourceConnection->getConnection();
            $tableName = $resourceConnection->getTableName('sales_shipment_track');

            $select = $connection->select()
                ->from($tableName, ['description'])
                ->where('track_number = ?', $trackNumber)
                ->limit(1);

            $description = $connection->fetchOne($select);

            return $description ?: null;
        } catch (\Exception $e) {
            // Log error but don't break the page
            return null;
        }
    }

    /**
     * Get tracking URL from description if it's a valid URL
     *
     * @return string|null
     */
    public function getTrackingUrl()
    {
        $description = $this->getTrackingDescription();

        if ($description && filter_var($description, FILTER_VALIDATE_URL)) {
            return $description;
        }

        return null;
    }



}
