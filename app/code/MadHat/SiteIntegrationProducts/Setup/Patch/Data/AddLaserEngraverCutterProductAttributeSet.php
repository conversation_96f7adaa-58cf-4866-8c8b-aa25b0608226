<?php
namespace MadHat\SiteIntegrationProducts\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\SetFactory;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

class AddLaserEngraverCutterProductAttributeSet implements DataPatchInterface
{
    protected const ATTRIBUTE_SET_NAME = 'Laser Engraver & Cutter';

    private const SORT_ORDER = 3;

    /**
     * @var ModuleDataSetupInterface
     */
    private ModuleDataSetupInterface $moduleDataSetup;

    /**
     * @var EavSetupFactory
     */
    private EavSetupFactory $eavSetupFactory;

    /**
     * @var SetFactory
     */
    private SetFactory $attributeSetFactory;

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     * @param SetFactory $attributeSetFactory
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory,
        SetFactory $attributeSetFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->attributeSetFactory = $attributeSetFactory;
    }

    /**
     * Create a new attribute set
     *
     * @throws LocalizedException
     * @throws \Exception
     */
    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();

        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $entityTypeId = $eavSetup->getEntityTypeId(Product::ENTITY);

        $defaultAttributeSetId = $eavSetup->getDefaultAttributeSetId($entityTypeId);

        $attributeSet = $this->attributeSetFactory->create();
        $data = [
            'entity_type_id' => $entityTypeId,
            'attribute_set_name' => self::ATTRIBUTE_SET_NAME,
            'sort_order' => self::SORT_ORDER,
        ];

        $attributeSet->addData($data);
        $attributeSet->save();

        $attributeSet->initFromSkeleton($defaultAttributeSetId);
        $attributeSet->save();

        $this->moduleDataSetup->endSetup();
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function getAliases(): array
    {
        return [];
    }
}
