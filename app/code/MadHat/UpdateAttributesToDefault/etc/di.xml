<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Catalog\Model\ResourceModel\Product\Collection">
        <plugin name="madhat_updateattributestodefault_product_collection" type="MadHat\UpdateAttributesToDefault\Plugin\Model\ResourceModel\Product\CollectionPlugin" sortOrder="10" disabled="false"/>
    </type>
</config>
