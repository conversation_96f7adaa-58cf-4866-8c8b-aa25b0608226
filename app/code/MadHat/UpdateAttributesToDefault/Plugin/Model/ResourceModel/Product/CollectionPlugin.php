<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace MadHat\UpdateAttributesToDefault\Plugin\Model\ResourceModel\Product;

use Magento\Catalog\Model\Product\Attribute\Repository;
use Magento\Framework\DB\Select;
use Magento\Store\Model\Store;
use Psr\Log\LoggerInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Catalog\Model\Product\Type;

class CollectionPlugin
{
    /**
     * @var Repository
     */
    protected $attributeRepository;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * CollectionPlugin constructor.
     * @param Repository $attributeRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        Repository $attributeRepository,
        LoggerInterface $logger
    ) {
        $this->attributeRepository = $attributeRepository;
        $this->logger = $logger;
    }

    /**
     * @param \Magento\Catalog\Model\ResourceModel\Product\Collection $subject
     * @param $field
     * @param $condition
     * @return array
     */
    public function aroundAddFieldToFilter(
        \Magento\Catalog\Model\ResourceModel\Product\Collection $subject,
        callable $proceed,
        $field,
        $condition = null
    ) {
        if (isset($condition['eq']) && $this->isStoreAttribute($field)) {
            $attributeCode = $condition['eq'];

            $this->logger->info(
                'Store attributes filter triggered',
                ['field' => $field, 'condition' => $condition, 'attribute_code' => $attributeCode]
            );

            // Validate attribute code
            if (empty($attributeCode) || !is_string($attributeCode)) {
                $this->logger->warning(
                    'Invalid attribute code provided for store_attributes filter',
                    ['attribute_code' => $attributeCode, 'condition' => $condition]
                );
                // Return the collection without applying any filter
                return $subject;
            }

            try {
                $attribute = $this->attributeRepository->get($attributeCode);

                $this->logger->info(
                    'Attribute retrieved successfully',
                    ['attribute_code' => $attributeCode, 'scope' => $attribute->getIsGlobal()]
                );

                // Verify the attribute is website or store-scoped (not global)
                $attributeScope = $attribute->getIsGlobal();
                $this->logger->info(
                    'Checking attribute scope',
                    [
                        'attribute_code' => $attributeCode,
                        'scope' => $attributeScope,
                        'expected_scopes' => [ScopedAttributeInterface::SCOPE_WEBSITE, ScopedAttributeInterface::SCOPE_STORE],
                        'is_website_or_store_scoped' => in_array($attributeScope, [ScopedAttributeInterface::SCOPE_WEBSITE, ScopedAttributeInterface::SCOPE_STORE])
                    ]
                );

                // Only allow website-scoped or store-scoped attributes
                if (!in_array($attributeScope, [ScopedAttributeInterface::SCOPE_WEBSITE, ScopedAttributeInterface::SCOPE_STORE])) {
                    $this->logger->warning(
                        'Attribute is not website or store-scoped for store_attributes filter',
                        ['attribute_code' => $attributeCode, 'scope' => $attributeScope]
                    );
                    // Return the collection without applying any filter
                    return $subject;
                }

                $this->logger->info(
                    'Attribute is website or store-scoped, applying custom filter',
                    ['attribute_code' => $attributeCode, 'scope' => $attributeScope]
                );

                // Apply the custom filter logic to show products with store-specific values
                $this->applyStoreAttributeFilter($subject, $attribute);

                // Return the collection with our custom filter applied
                return $subject;

            } catch (NoSuchEntityException $e) {
                $this->logger->error(
                    'Attribute not found for store_attributes filter',
                    ['attribute_code' => $attributeCode, 'error' => $e->getMessage()]
                );
                // Return the collection without applying any filter
                return $subject;
            } catch (\Exception $e) {
                $this->logger->error(
                    'Error processing store_attributes filter',
                    ['attribute_code' => $attributeCode, 'error' => $e->getMessage()]
                );
                // Return the collection without applying any filter
                return $subject;
            }
        }

        // For all other filters, proceed normally
        return $proceed($field, $condition);
    }

    /**
     * Apply custom filter to show products with website or store-specific attribute values
     *
     * Products are shown if the selected attribute has a value not only for the Default scope
     * but also for the Website or Store scope. Products where the selected attribute has a value
     * only in the Default scope are hidden.
     *
     * Only simple products are shown when this filter is applied, configurable products are hidden.
     *
     * @param \Magento\Catalog\Model\ResourceModel\Product\Collection $subject
     * @param \Magento\Catalog\Model\ResourceModel\Eav\Attribute $attribute
     * @return void
     */
    private function applyStoreAttributeFilter($subject, $attribute)
    {
        $attributeTable = $attribute->getBackend()->getTable();
        $attributeId = $attribute->getId();
        $select = $subject->getSelect();

        // We want to show products that have website or store-specific values (not using default only)
        // This means there should be records in the attribute table for non-default stores
        // Store ID 0 = Default scope, any other store ID = Website or Store scope

        // Join website/store-specific attribute values (any non-default store)
        $select->joinInner(
            ['store_attr' => $attributeTable],
            'e.entity_id = store_attr.entity_id AND store_attr.attribute_id = ' . $attributeId . ' AND store_attr.store_id != ' . Store::DEFAULT_STORE_ID,
            []
        );

        // Only show simple products, hide configurable products
        $subject->addAttributeToFilter('type_id', Type::TYPE_SIMPLE);

        // Group by entity_id to avoid duplicates when a product has values in multiple stores
        $select->group('e.entity_id');

        $this->logger->info(
            'Applied store attributes filter',
            [
                'attribute_code' => $attribute->getAttributeCode(),
                'attribute_id' => $attributeId,
                'attribute_table' => $attributeTable,
                'scope' => $attribute->getIsGlobal(),
                'product_type_filter' => Type::TYPE_SIMPLE,
                'sql' => $select->__toString()
            ]
        );
    }

    /**
     * @param $field
     * @return bool
     */
    private function isStoreAttribute($field)
    {
        return $field === 'store_attributes';
    }
}
