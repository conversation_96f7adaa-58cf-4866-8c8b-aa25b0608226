<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace MadHat\UpdateAttributesToDefault\Ui\Component\Form\DataProvider\Product;

use Magento\Ui\DataProvider\AbstractDataProvider;
use Magento\Catalog\Model\ResourceModel\Product\Attribute\CollectionFactory as AttributeCollectionFactory;
use Magento\Catalog\Api\Data\ProductAttributeInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;

class AttributesToDefault extends AbstractDataProvider
{
    /**
     * @var AttributeCollectionFactory
     */
    protected $attributeCollectionFactory;

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * @var EavConfig
     */
    protected $eavConfig;

    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param AttributeCollectionFactory $attributeCollectionFactory
     * @param RequestInterface $request
     * @param EavConfig $eavConfig
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        AttributeCollectionFactory $attributeCollectionFactory,
        RequestInterface $request,
        EavConfig $eavConfig,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->attributeCollectionFactory = $attributeCollectionFactory;
        $this->request = $request;
        $this->eavConfig = $eavConfig;
        $this->collection = $this->attributeCollectionFactory->create(); // Not strictly used for rows, but good to have
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        // This DataProvider is primarily for providing the list of attributes to the form,
        // not for loading data of a specific entity.
        // The 'data' part might be used to pre-fill selections if needed, but for now, it's empty.
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }
        $this->loadedData = [];
        // We pass selected product IDs to the save controller, not really needed for form rendering here.
        // $items = $this->request->getParam($this->requestFieldName, []);
        // $this->loadedData[null]['product_ids'] = $items; // Example if we needed to pass it

        return $this->loadedData;
    }

    /**
     * Get meta
     *
     * @return array
     */
    public function getMeta()
    {
        $meta = parent::getMeta();
        $meta['attributes_details']['children'] = $this->getAttributeFields();
        return $meta;
    }

    /**
     * Get attribute fields for the form
     *
     * @return array
     */
    protected function getAttributeFields()
    {
        $fields = [];
        $attributeCollection = $this->attributeCollectionFactory->create()
            ->addVisibleFilter()
            // Include both website-scoped and store-scoped attributes (same as StoreAttributes filter)
            // These are attributes that can have different values per website or store
            ->addFieldToFilter(
                'is_global',
                ['in' => [ScopedAttributeInterface::SCOPE_WEBSITE, ScopedAttributeInterface::SCOPE_STORE]]
            )
            ->setOrder(ProductAttributeInterface::FRONTEND_LABEL, 'ASC');

        // Only show attributes that can have store-specific values that can be reset to default.
        // This includes website-scoped and store-scoped attributes only.
        // Global attributes don't have store-specific values, so they can't be "reset to default".
        // This matches the same filtering logic used in the "Storefront Attributes" filter.

        foreach ($attributeCollection as $attribute) {
            /** @var ProductAttributeInterface $attribute */
            // Ensure attribute can be applied to products (though addVisibleFilter should handle most of this)
            // if (!$attribute->getIsGlobal() && !$attribute->getApplyTo()) {
            //    continue;
            // }


            $fields[$attribute->getAttributeCode()] = [
                'arguments' => [
                    'data' => [
                        'config' => [
                            'label' => __('%1', $attribute->getStoreLabel()),
                            'dataType' => 'boolean',
                            'formElement' => 'checkbox',
                            'componentType' => 'field',
                            'prefer' => 'toggle',
                            'valueMap' => [
                                'true' => '1',
                                'false' => '0',
                            ],
                            'default' => '0', // Default to unchecked
                            'notice' => __('Set %1 to default value.', $attribute->getStoreLabel()),
                            'dataScope' => 'attributes_to_reset.' . $attribute->getAttributeCode(), // This will group them under an array
                            'sortOrder' => (int)$attribute->getPosition() ?: 100, // Basic sorting
                        ],
                    ],
                ],
            ];
        }
        return $fields;
    }
}
