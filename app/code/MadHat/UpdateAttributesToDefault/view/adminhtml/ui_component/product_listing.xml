<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <listingToolbar name="listing_top">
        <filters name="listing_filters">
            <filterSelect name="store_attributes" provider="${ $.parentName }">
                <settings>
                    <options class="MadHat\UpdateAttributesToDefault\Model\Config\Source\StoreAttributes"/>
                    <caption translate="true">-- Please Select --</caption>
                    <label translate="true">Storefront Attributes</label>
                    <dataScope>store_attributes</dataScope>
                    <imports>
                        <link name="visible">ns = ${ $.ns }, index = ${ $.index }:visible</link>
                    </imports>
                </settings>
            </filterSelect>
        </filters>
        <massaction name="listing_massaction">
            <action name="madhat_update_attributes_to_default">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">madhat_update_attributes_to_default</item>
                        <item name="label" xsi:type="string" translate="true">MadHat Update attributes to default</item>
                        <item name="url" xsi:type="url" path="madhat_updateattributestodefault/product_action/editdefault"/>
                        <item name="aclResource" xsi:type="string">MadHat_UpdateAttributesToDefault::action_update_default</item>
                    </item>
                </argument>
            </action>
        </massaction>
    </listingToolbar>
    <columns name="product_columns">
        <column name="store_attributes" class="MadHat\UpdateAttributesToDefault\Ui\Component\Listing\Column\StoreAttributes">
            <settings>
                <filter>select</filter>
                <options class="MadHat\UpdateAttributesToDefault\Model\Config\Source\StoreAttributes"/>
                <dataType>select</dataType>
                <label translate="true">Storefront Attributes</label>
                <sortable>false</sortable>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
            </settings>
        </column>
    </columns>
</listing>
