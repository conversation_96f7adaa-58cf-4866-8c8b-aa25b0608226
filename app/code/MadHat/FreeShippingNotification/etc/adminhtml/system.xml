<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="freeshipping" translate="label" type="text" sortOrder="500" showInDefault="1" showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>Free Shipping Notification Configs</label>
            <tab>madhat</tab>
            <resource>MadHat_FreeShippingNotification::config</resource>
            <group id="general" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Shipping Notification Config</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <comment>If the Shipping Notification on cart and checkout is enabled.</comment>
                    <validate>required-entry</validate>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="amount" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Free Shipping Over</label>
                    <comment>Free Shipping Over X amount. Put the X value here.</comment>
                    <validate>validate-zero-or-greater required-entry</validate>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="custom_text" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>FreeShipping Over Text</label>
                    <comment>Text on Frontend for customers</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="free_delivery_text" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>FreeShipping Text</label>
                    <comment>Text on Frontend for customers</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
