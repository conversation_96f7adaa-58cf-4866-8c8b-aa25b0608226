<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="madhat_run_cron_job" xsi:type="object">MadHat\StockAlert\Console\RunCronJobDirectly</item>
            </argument>
        </arguments>
    </type>
    <preference for="Magento\ProductAlert\Model\Observer" type="MadHat\StockAlert\Model\StockAlertObserver"/>
    <preference for="Magento\ProductAlert\Model\Mailing\AlertProcessor"
                type="MadHat\StockAlert\Model\Mailing\StockAlertProcessor"/>
    <preference for="Magento\ProductAlert\Model\Email" type="MadHat\StockAlert\Model\StockAlertEmail"/>

</config>

