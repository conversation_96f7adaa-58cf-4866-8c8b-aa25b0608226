<?php
namespace MadHat\StockAlert\Console;

use Magento\Framework\App\State;
use Magento\ProductAlert\Model\Observer;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class RunCronJobDirectly extends Command
{
    protected $state;
    protected $cronJob;

    public function __construct(
        State $state,
        Observer $cronJob
    ) {
        $this->state = $state;
        $this->cronJob = $cronJob;
        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('madhat:run-cron-job')
            ->setDescription('Run the cron job MyJob directly');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->state->setAreaCode('crontab');
        $this->cronJob->process();
        $output->writeln('<info>Executed MyJob cron manually.</info>');
        return 0;
    }
}
