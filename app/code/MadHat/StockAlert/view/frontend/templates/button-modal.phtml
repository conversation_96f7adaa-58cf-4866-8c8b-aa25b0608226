<?php
/** @var \MadHat\StockAlert\Block\StockAlert $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Theme\ViewModel\Modal $modalViewModel */
$escaper = $block->escapeHtmlAttr(...);
$modalViewModel = $viewModels->require(\Hyva\Theme\ViewModel\Modal::class);
$isSalable = $block->getProduct()->isSalable();
?>
<div
    x-data="Object.assign({}, hyva.modal(), {
        loggedInCustomerEmail: '',
        email: '',
        entity_id: '<?= $escaper($block->getProduct()->getId() ?: 0) ?>',
        stock_status:1,
        saving: false,
        simpleDetailProductActiveEvent(eventDetail) {
            this.entity_id = eventDetail.product.entity;
            this.stock_status = !eventDetail.event_product_data.stock_status;
        },
        submit() {
            const formKey = hyva.getFormKey();
            if (!this.email) { window.dispatchMessages([{type:'error', text:'<?= $escaper(__('Please enter a valid email.')) ?>'}]); return; }
            this.saving = true;
            fetch('<?= $escaper($block->getPostUrl()) ?>', {
                method : 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
                },
                body   : new URLSearchParams({
                    form_key: formKey,
                    product_id: this.entity_id,
                    email     : this.email
                })
            }).then(
                res => res.json()
            ).then(data => {
                this.saving = false;
                this.hide();
                if (data.success) {
                    window.dispatchMessages([{type:'warning', text:data.message}]);
                } else {
                    window.dispatchMessages([{type:'error', text:data.message}]);
                }
            }).catch(() => {
                this.saving = false;
                window.dispatchMessages([{type:'error', text:'<?= $escaper(__('Something went wrong.')) ?>'}]);
            });
        }
    })"
    @private-content-loaded.window.once="
        email = loggedInCustomerEmail = $event.detail.data.customer?.email || ''
      "
>

    <?php if ($block->getProduct()->getTypeId() == 'configurable'): ?>
        <button
            type="button"
            class="btn btn-primary w-full btn-size-md md:w-auto"
            @click="show"
            aria-haspopup="dialog"
            :class="{'disable hidden': !stock_status}"
            x-bind:disabled=!stock_status
            @simple-detail-product-active.window="simpleDetailProductActiveEvent($event.detail)"
        >
            <?= $escaper(__('Notify me when it Back')) ?>
        </button>
    <?php else: ?>
        <button
            type="button"
            class="btn btn-primary w-full btn-size-md md:w-auto <?= ($isSalable ? 'disable hidden' : ''); ?>"
            <?= ($isSalable ? 'disabled="disabled"' : ''); ?>
            @click="show"
            aria-haspopup="dialog"
        >
            <?= $escaper(__('Notify me when it Back')) ?>
        </button>
    <?php endif; ?>

    <?php
    $modalHtml = <<<HTML
        <div class="p-6 sm:p-8 w-full max-w-md">
            <h2 id="notify-label" class="text-lg font-semibold mb-4 text-gray-800">
                {$escaper(__('Get Stock Alert Notification'))}
            </h2>
            <form @submit.prevent="submit" class="space-y-4">
                <template x-if="!loggedInCustomerEmail">
                    <div>
                        <label for="notify-email" class="block text-sm font-medium text-gray-700 mb-1">
                            {$escaper(__('Provide your E-mail address to get Notification when this Product is back in stock again.'))}
                        </label>
                        <input x-model="email" id="notify-email" type="email" required
                               class="w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"/>
                    </div>
                </template>
                <template x-if="loggedInCustomerEmail">
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-600">{$escaper(__('You are logged in as'))}</span>
                        <span class="px-2 py-1 bg-gray-100 rounded text-gray-800 text-sm" x-text="loggedInCustomerEmail"></span>
                    </div>
                </template>
                <button type="submit"
                        class="btn btn-primary w-full flex justify-center"
                        :class="saving && 'opacity-50 cursor-not-allowed'"
                        :disabled="saving">
                    <span x-show="!saving">{$escaper(__('Notify Me via Email.'))}</span>
                    <span x-show="saving" x-cloak>{$escaper(__('Saving Email Address ...'))}</span>
                </button>
            </form>
        </div>
    HTML;

    echo /** @noEscape */
    $modalViewModel->createModal()
        ->withContent($modalHtml)
        ->withAriaLabelledby('notify-label')
        ->addDialogClass('bg-white', 'rounded', 'shadow-xl', 'max-w-lg', 'w-full')
        ->addOverlayClass('bg-black', 'bg-opacity-40');
    ?>
</div>
