<?php
declare(strict_types=1);

namespace MadHat\StockAlert\Block;

use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template;

class StockAlert extends Template
{
    private CustomerSession $customerSession;
    private Registry        $registry;

    public function __construct(
        Template\Context $context,
        CustomerSession  $customerSession,
        Registry         $registry,
        \Magento\Framework\App\RequestInterface $request,
        array            $data = []
    ) {
        parent::__construct($context, $data);
        $this->customerSession = $customerSession;
        $this->registry        = $registry;
        $this->request = $request;
    }

    public function isLoggedIn(): bool
    {
        return $this->customerSession->isLoggedIn();
    }

    public function getCustomerEmail(): ?string
    {
        return $this->customerSession->getCustomer()->getEmail();
    }

    public function getCustomerId(): ?string
    {
        return $this->customerSession->getCustomer()->getId();
    }

    public function getPostUrl(): string
    {
        return $this->getUrl('productalert/add/guestStock');
    }

    public function getProduct(): \Magento\Catalog\Model\Product
    {
        //echo "<pre>";print_r($this->request->getParams());exit;
        /** @var \Magento\Catalog\Model\Product $product */
        $product = $this->registry->registry('current_product');
        return $product;
    }

    public function getProductId(): int
    {

        return (int)$this->getProduct()->getId();
    }
}
