<?php
declare(strict_types=1);

namespace MadHat\StockAlert\Controller\Add;

use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Framework\Message\ManagerInterface;
use Magento\ProductAlert\Model\StockFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\RequestInterface;
use Laminas\Validator\EmailAddress;
use MadHat\StockAlert\Block\StockAlert;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\ProductAlert\Model\ResourceModel\Stock\CollectionFactory as StockCollectionFactory;

class GuestStock implements HttpPostActionInterface
{
    private ManagerInterface $messageManager;
    private RedirectFactory $resultRedirectFactory;
    private StockFactory $stockFactory;
    private DateTime $date;
    private StoreManagerInterface $storeManager;
    protected RequestInterface $request;
    protected EmailAddress $emailValidator;
    protected StockAlert $stockAlert;
    protected JsonFactory $resultJsonFactory;

    /**
     * @var StockCollectionFactory
     */
    private $stockCollectionFactory;

    public function __construct(
        Context               $context,
        ManagerInterface      $messageManager,
        RedirectFactory       $resultRedirectFactory,
        StockFactory          $stockFactory,
        DateTime              $date,
        StoreManagerInterface $storeManager,
        RequestInterface $request,
        EmailAddress $emailValidator,
        StockAlert $stockAlert,
        JsonFactory $resultJsonFactory,
        StockCollectionFactory $stockCollectionFactory
    )
    {
        $this->messageManager = $messageManager;
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->stockFactory = $stockFactory;
        $this->storeManager = $storeManager;
        $this->emailValidator = $emailValidator;
        $this->stockAlert = $stockAlert;
        $this->request = $request;
        $this->date = $date;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->stockCollectionFactory = $stockCollectionFactory;
    }

    public function execute()
    {
        $message = '';
        $success = false;
        // use storeManager only for scope data
        $result = $this->resultJsonFactory->create();
        $request = $this->storeManager->getStore()->getWebsiteId();
        $productId = (int)$this->request->getParam('product_id');
        $email = trim($this->request->getParam('email'));

        if (!$this->emailValidator->isValid($email)) {
            $this->messageManager->addErrorMessage(__('Please enter a valid email.'));
            return $this->resultRedirectFactory->create()->setPath('catalog/product/view', ['id' => $productId]);
        }

        if ($this->stockAlert->isLoggedIn()) {
            $customerId = $this->stockAlert->getCustomerId();
        } else {
            $customerId = null;
        }

        $alertCollection = $this->stockCollectionFactory->create();
        $alertCollection->addFieldToFilter('guest_email', $email)
            ->addFieldToFilter('product_id', $productId)
            ->addFieldToFilter('website_id', $this->storeManager->getStore()->getWebsiteId())
            ->addFieldToFilter('store_id', $this->storeManager->getStore()->getId());


        if ($alertCollection->count() == 0) {
            $alert = $this->stockFactory->create();
            try {
                $alert->setData([
                    'product_id' => $productId,
                    'website_id' => $this->storeManager->getStore()->getWebsiteId(),
                    'store_id' => $this->storeManager->getStore()->getId(),
                    'guest_email' => $email,
                    'customer_id' => $customerId,
                    'add_date' => $this->date->gmtDate(),
                ])->save();

                $success = true;
                $message = __('Thanks! We will email you when the item is back in stock.');
            } catch (NoSuchEntityException $noEntityException) {
                $success = false;
                $message = __('There are not enough parameters.');
            } catch (\Exception $e) {
                $success = false;
                $message = __("The alert subscription couldn't update at this time. Please try again later.");
            }
        } else {
            $success = false;
            $message = __("You already have subscribed to this product.");
        }

        return $result->setData([
            'success' => $success,
            'message' => $message
        ]);
    }
}
