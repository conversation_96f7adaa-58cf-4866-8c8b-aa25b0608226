<?php

namespace MadHat\SiteIntegrationOrderstatus\Model\Data;

use MadHat\SiteIntegrationOrderstatus\Api\Data\SerialNumbersInterface;
use Magento\Framework\DataObject;

class SerialNumbers extends DataObject implements SerialNumbersInterface
{
    public function getSerialNumber()
    {
        return $this->getData('SerialNumber');
    }

    public function setSerialNumber(?string $serialNumber)
    {
        $this->setData('SerialNumber', $serialNumber);
    }
}
