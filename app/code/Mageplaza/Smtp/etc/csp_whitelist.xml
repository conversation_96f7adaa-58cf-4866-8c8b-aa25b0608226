<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Smtp
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
-->
<csp_whitelist xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Csp:etc/csp_whitelist.xsd">
    <policies>
        <policy id="script-src">
            <values>
                <value id="avada" type="host">*.avada.io</value>
            </values>
        </policy>
        <policy id="connect-src">
            <values>
                <value id="geojs" type="host">https://get.geojs.io</value>
                <value id="avada" type="host">*.avada.io</value>
            </values>
        </policy>
        <policy id="style-src">
            <values>
                <value id="fonts_bunny" type="host">https://fonts.bunny.net</value>
            </values>
        </policy>
        <policy id="font-src">
            <values>
                <value id="fonts_bunny" type="host">https://fonts.bunny.net</value>
            </values>
        </policy>
        <policy id="img-src">
            <values>
                <value id="firebase-storage" type="host">https://firebasestorage.googleapis.com</value>
            </values>
        </policy>
    </policies>
</csp_whitelist>

