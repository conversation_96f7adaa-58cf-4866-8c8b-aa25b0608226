{"name": "mageplaza/module-smtp", "description": "SMTP Extension for Magento 2 helps the owner of store simply install SMTP (Simple Mail Transfer Protocol) server which transmits the messages into codes or numbers", "require": {"mageplaza/module-core": "^1.5.9"}, "type": "magento2-module", "version": "4.7.14", "license": "proprietary", "authors": [{"name": "Mageplaza", "email": "<EMAIL>", "homepage": "https://www.mageplaza.com", "role": "Technical Support"}], "minimum-stability": "dev", "keywords": ["magento 2", "magento 2 smtp", "magento smtp", "mageplaza", "smtp", "email settings", "gmail smtp", "marketing automation", "email marketing", "sms marketing", "sms", "email", "messenger", "facebook", "google ads", "facebook ads", "whatsapp", "web push notification"], "autoload": {"files": ["registration.php"], "psr-4": {"Mageplaza\\Smtp\\": ""}}}