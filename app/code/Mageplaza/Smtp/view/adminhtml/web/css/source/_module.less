/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Smtp
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
.mpsmtp-modal-email {
  .modal-content {
    height: 100%;

    > div {
      height: 100%;
    }
  }
}

#mageplaza_smtp_provider {
  width: calc(~"100% - 125px")
}

.actions-provider-button {
  width: 121px;
  box-sizing: border-box;
}

#smtp-abandoned-cart .tooltip {
  position: relative;
  display: inline-block;
}

#smtp-abandoned-cart .tooltip .tooltip-text {
  visibility: hidden;
  width: 140px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 150%;
  left: 50%;
  margin-left: -75px;
  opacity: 0;
  transition: opacity 0.3s;
}

#smtp-abandoned-cart .tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #555 transparent transparent transparent;
}

#smtp-abandoned-cart .tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}