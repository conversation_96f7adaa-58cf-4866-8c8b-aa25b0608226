/* Slovak initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.sk = {
	closeText: "Zavrieť",
	prevText: "&#x3C;Predchádzajúci",
	nextText: "Nasledujúci&#x3E;",
	currentText: "Dnes",
	monthNames: [ "janu<PERSON>r", "febru<PERSON>r", "marec", "apr<PERSON>l", "máj", "jún",
	"júl", "august", "september", "október", "november", "december" ],
	monthNamesShort: [ "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Okt", "Nov", "<PERSON>" ],
	dayNames: [ "nedeľa", "pondelok", "utorok", "streda", "štvrtok", "piatok", "sobota" ],
	dayNamesShort: [ "Ned", "Pon", "Uto", "Str", "Štv", "Pia", "Sob" ],
	dayNamesMin: [ "Ne", "Po", "Ut", "St", "Št", "Pia", "So" ],
	weekHeader: "Ty",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.sk );

return datepicker.regional.sk;

} );
