/* Esperanto initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.eo = {
	closeText: "Fermi",
	prevText: "&#x3C;Anta",
	nextText: "Sekv&#x3E;",
	currentText: "Nuna",
	monthNames: [ "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
	"<PERSON>", "Aŭgus<PERSON>", "Septembro", "Oktobro", "Novembro", "Decembro" ],
	monthNamesShort: [ "Jan", "Feb", "<PERSON>", "Apr", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>ŭg", "<PERSON>", "<PERSON><PERSON>", "Nov", "Dec" ],
	dayNames: [ "Dimanĉo", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ĵaŭdo", "<PERSON><PERSON><PERSON>", "<PERSON>bato" ],
	dayNamesShort: [ "Dim", "Lun", "Mar", "Mer", "Ĵaŭ", "Ven", "Sab" ],
	dayNamesMin: [ "Di", "Lu", "Ma", "Me", "Ĵa", "Ve", "Sa" ],
	weekHeader: "Sb",
	dateFormat: "dd/mm/yy",
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.eo );

return datepicker.regional.eo;

} );
