<?php
/**
 * Test script to verify the Storefront Attributes filter functionality
 */

use Magento\Framework\App\Bootstrap;
use Magento\Catalog\Model\ResourceModel\Product\Attribute\CollectionFactory;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use MadHat\UpdateAttributesToDefault\Model\Config\Source\StoreAttributes;

require __DIR__ . '/app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

echo "=== Testing Storefront Attributes Filter ===\n\n";

// Test 1: Check StoreAttributes source options
echo "1. Testing StoreAttributes source options:\n";
$storeAttributesSource = $objectManager->create(StoreAttributes::class);
$options = $storeAttributesSource->toOptionArray();

echo "Found " . count($options) . " attributes:\n";
foreach ($options as $option) {
    echo "  - {$option['label']} ({$option['value']})\n";
}

// Test 2: Check attribute scopes
echo "\n2. Testing attribute scopes:\n";
$attributeCollectionFactory = $objectManager->create(CollectionFactory::class);
$attributeCollection = $attributeCollectionFactory->create();
$attributeCollection->addFieldToFilter(
    'is_global',
    ['in' => [ScopedAttributeInterface::SCOPE_WEBSITE, ScopedAttributeInterface::SCOPE_STORE]]
);
$attributeCollection->addVisibleFilter();
$attributeCollection->setPageSize(10); // Limit for testing

echo "Attributes with Website or Store scope:\n";
foreach ($attributeCollection as $attribute) {
    $scopeText = '';
    switch ($attribute->getIsGlobal()) {
        case ScopedAttributeInterface::SCOPE_GLOBAL:
            $scopeText = 'Global';
            break;
        case ScopedAttributeInterface::SCOPE_WEBSITE:
            $scopeText = 'Website';
            break;
        case ScopedAttributeInterface::SCOPE_STORE:
            $scopeText = 'Store';
            break;
    }
    echo "  - {$attribute->getFrontendLabel()} ({$attribute->getAttributeCode()}) - Scope: {$scopeText}\n";
}

echo "\n=== Test completed ===\n";
