<?php
/**
 * Test script to verify that form and filter show the same attributes
 */

use Magento\Framework\App\Bootstrap;

require __DIR__ . '/app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

echo "=== Testing Attribute Consistency ===\n\n";

// Test 1: Get attributes from StoreAttributes source (filter)
echo "1. Testing StoreAttributes source (filter):\n";
$storeAttributesSource = $objectManager->create(\MadHat\UpdateAttributesToDefault\Model\Config\Source\StoreAttributes::class);
$filterOptions = $storeAttributesSource->toOptionArray();

$filterAttributes = [];
foreach ($filterOptions as $option) {
    $filterAttributes[] = $option['value'];
}

echo "   - Found " . count($filterAttributes) . " attributes in filter\n";
echo "   - First 10: " . implode(', ', array_slice($filterAttributes, 0, 10)) . "\n";

// Test 2: Get attributes from DataProvider (form)
echo "\n2. Testing AttributesToDefault DataProvider (form):\n";
$dataProvider = $objectManager->create(
    \MadHat\UpdateAttributesToDefault\Ui\Component\Form\DataProvider\Product\AttributesToDefault::class,
    [
        'name' => 'test_data_provider',
        'primaryFieldName' => 'entity_id',
        'requestFieldName' => 'ids'
    ]
);

$meta = $dataProvider->getMeta();
$formAttributes = [];

if (isset($meta['attributes_details']['children'])) {
    $formAttributes = array_keys($meta['attributes_details']['children']);
}

echo "   - Found " . count($formAttributes) . " attributes in form\n";
echo "   - First 10: " . implode(', ', array_slice($formAttributes, 0, 10)) . "\n";

// Test 3: Compare the two lists
echo "\n3. Comparing attribute lists:\n";

$filterSet = array_flip($filterAttributes);
$formSet = array_flip($formAttributes);

// Find attributes in filter but not in form
$inFilterNotForm = array_diff_key($filterSet, $formSet);
if (!empty($inFilterNotForm)) {
    echo "   - Attributes in filter but NOT in form: " . implode(', ', array_keys($inFilterNotForm)) . "\n";
} else {
    echo "   ✓ All filter attributes are in form\n";
}

// Find attributes in form but not in filter
$inFormNotFilter = array_diff_key($formSet, $filterSet);
if (!empty($inFormNotFilter)) {
    echo "   - Attributes in form but NOT in filter: " . implode(', ', array_keys($inFormNotFilter)) . "\n";
} else {
    echo "   ✓ All form attributes are in filter\n";
}

// Check if they match exactly
if (count($filterAttributes) === count($formAttributes) && empty($inFilterNotForm) && empty($inFormNotFilter)) {
    echo "   ✅ PERFECT MATCH: Filter and form show exactly the same attributes!\n";
} else {
    echo "   ⚠️  MISMATCH: Filter and form show different attributes\n";
    echo "      Filter count: " . count($filterAttributes) . "\n";
    echo "      Form count: " . count($formAttributes) . "\n";
}

// Test 4: Show some example attributes with their scopes
echo "\n4. Sample attributes with scopes:\n";
$eavConfig = $objectManager->get(\Magento\Eav\Model\Config::class);

$sampleAttributes = array_slice($filterAttributes, 0, 5);
foreach ($sampleAttributes as $attrCode) {
    try {
        $attribute = $eavConfig->getAttribute('catalog_product', $attrCode);
        $scope = $attribute->getIsGlobal();
        $scopeText = '';
        switch ($scope) {
            case \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_GLOBAL:
                $scopeText = 'Global';
                break;
            case \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_WEBSITE:
                $scopeText = 'Website';
                break;
            case \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE:
                $scopeText = 'Store';
                break;
        }
        echo "   - {$attrCode}: {$scopeText} scope\n";
    } catch (\Exception $e) {
        echo "   - {$attrCode}: Error getting scope\n";
    }
}

echo "\n=== Test completed ===\n";
